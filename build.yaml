targets:
  $default:
    builders:
      # Temporarily disable injectable to avoid conflicts
      injectable_generator:injectable_builder:
        enabled: false

      freezed:freezed:
        enabled: true
        options:
          # Generate copyWith, toString, operator==, hashCode
          # and other useful methods

      json_serializable:json_serializable:
        enabled: true
        options:
          # Generate toJson and fromJson methods
          any_map: false
          checked: false
          create_factory: true
          create_to_json: true
          disallow_unrecognized_keys: false
          explicit_to_json: false
          field_rename: none
          generic_argument_factories: false
          ignore_unannotated: false
          include_if_null: true
