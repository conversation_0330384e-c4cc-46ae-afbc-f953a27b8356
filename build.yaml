targets:
  $default:
    builders:
      injectable_generator:injectable_builder:
        enabled: true
        options:
          auto_register: true
          class_name_pattern: "^_?(.*)$"
          
      auto_route_generator:autoRouteGenerator:
        enabled: true
        options:
          route_prefix: ""
          
      freezed:freezed:
        enabled: true
        options:
          # Generate copyWith, toString, operator==, hashCode
          # and other useful methods
          
      json_serializable:json_serializable:
        enabled: true
        options:
          # Generate toJson and fromJson methods
          any_map: false
          checked: false
          create_factory: true
          create_to_json: true
          disallow_unrecognized_keys: false
          explicit_to_json: false
          field_rename: none
          generic_argument_factories: false
          ignore_unannotated: false
          include_if_null: true
