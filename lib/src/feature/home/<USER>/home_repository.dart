import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show
        HomeQuoteModel,
        HomeQuotesDeserializationException,
        FetchHomeQuotesException,
        AddHomeQuoteException;

class HomeRepository {
  const HomeRepository(
    FirebaseFirestore firestore,
  ) : _firestore = firestore;

  final FirebaseFirestore _firestore;

  static const String _collectionName = 'quotes';
  static const int _limit = 1;

  Future<List<HomeQuoteModel>> fetchHomeQuotes() async {
    try {
      final quotes = _firestore.collection(_collectionName);
      final key = quotes.doc().id;

      final snapshot = await quotes
          .where(FieldPath.documentId, isGreaterThanOrEqualTo: key)
          .limit(_limit)
          .get();

      if (snapshot.size > 0) {
        return snapshot.docs.toHomeQuotes();
      } else {
        final snapshot = await quotes
            .where(FieldPath.documentId, isLessThan: key)
            .limit(_limit)
            .get();
        return snapshot.docs.toHomeQuotes();
      }
    } on HomeQuotesDeserializationException {
      rethrow;
    } on Exception catch (error, stackTrace) {
      throw FetchHomeQuotesException(error, stackTrace);
    }
  }

  Future<void> addHomeQuote(HomeQuoteModel homeQuote) async {
    try {
      await _firestore.collection(_collectionName).add(
            homeQuote.toJson(),
          );
    } on Exception catch (error, stackTrace) {
      throw AddHomeQuoteException(error, stackTrace);
    }
  }
}

extension on List<QueryDocumentSnapshot> {
  List<HomeQuoteModel> toHomeQuotes() {
    final homeQuotes = <HomeQuoteModel>[];
    for (final document in this) {
      final data = document.data() as Map<String, dynamic>?;
      if (data != null) {
        try {
          homeQuotes.add(HomeQuoteModel.fromJson(data));
        } catch (error, stackTrace) {
          throw HomeQuotesDeserializationException(error, stackTrace);
        }
      }
    }
    return homeQuotes;
  }
}
