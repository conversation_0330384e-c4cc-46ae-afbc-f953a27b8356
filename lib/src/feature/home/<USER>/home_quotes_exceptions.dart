abstract class HomeQuotesException implements Exception {
  const HomeQuotesException(this.error, this.stackTrace);

  final Object error;
  final StackTrace stackTrace;
}

class FetchHomeQuotesException extends HomeQuotesException {
  const FetchHomeQuotesException(super.error, super.stackTrace);
}

class HomeQuotesDeserializationException extends HomeQuotesException {
  const HomeQuotesDeserializationException(super.error, super.stackTrace);
}

class AddHomeQuoteException extends HomeQuotesException {
  const AddHomeQuoteException(super.error, super.stackTrace);
}
