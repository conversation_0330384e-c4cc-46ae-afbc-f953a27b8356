import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;

class Quotes extends StatelessWidget {
  const Quotes({
    super.key,
    required this.changeQuote,
  });

  final String? changeQuote;

  @override
  Widget build(BuildContext context) {
    return Positioned.fill(
      top: 50,
      child: Align(
        alignment: Alignment.center,
        child: Padding(
          padding: const EdgeInsets.only(
            left: 18,
            right: 18,
          ).w,
          child: SingleChildScrollView(
            child: Text(
              changeQuote ?? '',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
                color: SdmPalette.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }
}
