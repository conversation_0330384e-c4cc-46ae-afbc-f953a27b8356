import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';

HomeQuoteModel homeQuoteModelFromJson(String str) =>
    HomeQuoteModel.fromJson(json.decode(str));

String homeQuoteModelToJson(HomeQuoteModel data) => json.encode(data.toJson());

class HomeQuoteModel {
  String? text;
  Timestamp? timestamp;

  HomeQuoteModel({
    this.text,
    this.timestamp,
  });

  HomeQuoteModel copyWith({
    String? text,
    Timestamp? timestamp,
  }) =>
      HomeQuoteModel(
        text: text ?? this.text,
        timestamp: timestamp ?? this.timestamp,
      );

  factory HomeQuoteModel.fromJson(Map<String, dynamic> json) => HomeQuoteModel(
        text: json["text"],
        timestamp: json["timestamp"],
      );

  Map<String, dynamic> toJson() => {
        "text": text,
        "timestamp": timestamp,
      };
}
