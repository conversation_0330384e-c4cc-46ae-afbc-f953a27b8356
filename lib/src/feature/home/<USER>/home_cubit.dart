import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:shridattmandir/src/core/core.dart'
    show SdmSecureStorage, CubitExt, SdmSecureStorageKeys;
import 'package:shridattmandir/src/feature/feature.dart'
    show
        CalendarEventsRepository,
        CalendarEventModel,
        VideoRepository,
        VideoModel,
        HomeRepository,
        HomeQuoteModel;
import 'package:shridattmandir/src/shared/shared.dart' show SdmToast;

part 'home_state.dart';

class HomeCubit extends Cubit<HomeState> {
  HomeCubit() : super(const HomeState());

  final CalendarEventsRepository _calendarEventsRepository =
      CalendarEventsRepository(
    FirebaseFirestore.instance,
  );
  final VideoRepository _videoRepository = VideoRepository(
    FirebaseFirestore.instance,
  );
  final HomeRepository _homeRepository = HomeRepository(
    FirebaseFirestore.instance,
  );

  final TextEditingController quoteController = TextEditingController();

  Future<void> fetchHomeQuotes() async {
    safeEmit(
      state.copyWith(
        loading: true,
      ),
    );
    try {
      final homeQuotes = await _homeRepository.fetchHomeQuotes();

      safeEmit(
        state.copyWith(
          homeQuote: homeQuotes.last,
        ),
      );
    } catch (error) {
      SdmToast.show(
        error.toString(),
      );
    }
    safeEmit(
      state.copyWith(
        loading: false,
      ),
    );
  }

  Future<void> addHomeQuote() async {
    safeEmit(
      state.copyWith(
        loading: true,
      ),
    );
    try {
      final quote = HomeQuoteModel(
        text: quoteController.text,
        timestamp: Timestamp.now(),
      );

      if ((quoteController.text).isEmpty) {
        throw Exception('Please enter quote');
      }

      await _homeRepository.addHomeQuote(quote);
      quoteController.clear();
      SdmToast.show(
        'Quote added successfully',
      );
    } catch (error) {
      SdmToast.show(
        error.toString(),
      );
    }
    safeEmit(
      state.copyWith(
        loading: false,
      ),
    );
  }

  Future<void> fetchCalendarEvents() async {
    safeEmit(
      state.copyWith(
        loading: true,
      ),
    );
    try {
      final calendarEvents =
          await _calendarEventsRepository.fetchCalendarEvents();

      final upcomingEvents = calendarEvents.where(
        (event) {
          if (event.datetime == null) return false;

          return event.datetime!.isAfter(
            DateTime.now(),
          );
        },
      ).toList();
      safeEmit(
        state.copyWith(
          calendarEvents: upcomingEvents.reversed.toList(),
        ),
      );
    } catch (error) {
      SdmToast.show(
        error.toString(),
      );
    }
    safeEmit(
      state.copyWith(
        loading: false,
      ),
    );
  }

  Future<void> fetchVideos() async {
    safeEmit(
      state.copyWith(
        loading: true,
      ),
    );

    try {
      final videos = await _videoRepository.fetchVideos();
      safeEmit(
        state.copyWith(
          videos: videos.videoModel,
        ),
      );
    } catch (error) {
      SdmToast.show(
        error.toString(),
      );
    }
    safeEmit(
      state.copyWith(
        loading: false,
      ),
    );
  }

  Future<void> inAppUpdate() async {
    if (!Platform.isAndroid || kDebugMode) return;
    try {
      final updateInfo = await InAppUpdate.checkForUpdate();
      if (updateInfo.updateAvailability == UpdateAvailability.updateAvailable) {
        final appUpdateResult = await InAppUpdate.performImmediateUpdate();

        switch (appUpdateResult) {
          case AppUpdateResult.inAppUpdateFailed:
            SdmToast.show(
              'Update failed',
            );
            break;
          case AppUpdateResult.success:
            SdmToast.show(
              'Update success',
            );
            break;
          case AppUpdateResult.userDeniedUpdate:
            SdmToast.show(
              'Update Denied',
            );
            break;
        }
      }
    } catch (e) {
      SdmToast.show(
        'Something went wrong while updating: ${e.toString()}',
      );
    }
  }

  Future<void> inAppReview() async {
    final inAppReview = InAppReview.instance;

    //ask every 15 days
    final lastReviewDate = await SdmSecureStorage.readInt(
        key: SdmSecureStorageKeys.kInAppReviewLastShown);
    if (lastReviewDate != null) {
      final difference = DateTime.now().difference(
        DateTime.fromMillisecondsSinceEpoch(
          lastReviewDate,
        ),
      );

      if (difference.inDays < 15) {
        return;
      }

      if (await inAppReview.isAvailable()) {
        await inAppReview.requestReview();
        await SdmSecureStorage.writeInt(
          key: SdmSecureStorageKeys.kInAppReviewLastShown,
          value: DateTime.now().millisecondsSinceEpoch,
        );
      }
    }
  }

  @override
  Future<void> close() {
    quoteController.dispose();
    return super.close();
  }
}
