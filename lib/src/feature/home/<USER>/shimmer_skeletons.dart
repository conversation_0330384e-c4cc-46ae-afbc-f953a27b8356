import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show Assets, SdmPalette;

class UpcomingEventsSkeleton extends StatelessWidget {
  const UpcomingEventsSkeleton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(
            left: 10.0.w,
            top: 18.0.h,
          ),
          child: Text(
            'UPCOMING EVENTS',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14.sp,
            ),
          ),
        ),
        SizedBox(
          height: 14.h,
        ),
        AspectRatio(
          aspectRatio: 2.8,
          child: ListView.builder(
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.only(
              left: 10.0,
              right: 10.0,
            ).w,
            scrollDirection: Axis.horizontal,
            itemCount: 4,
            itemBuilder: (context, count) {
              return SizedBox(
                width: 150.0.w,
                child: Assets.sdmImages.eventCal.image(
                  fit: BoxFit.contain,
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

class LatestVideosSkeleton extends StatelessWidget {
  const LatestVideosSkeleton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 20.h,
        ),
        Padding(
          padding: const EdgeInsets.only(
            left: 10.0,
          ).w,
          child: Text(
            'LATEST VIDEOS',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14.sp,
            ),
          ),
        ),
        SizedBox(
          height: 14.h,
        ),
        AspectRatio(
          aspectRatio: 2.8,
          child: ListView.separated(
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.only(
              left: 10.0,
              right: 10.0,
            ).w,
            scrollDirection: Axis.horizontal,
            itemCount: 4,
            separatorBuilder: (context, index) {
              return SizedBox(
                width: 12.w,
              );
            },
            itemBuilder: (context, count) {
              return Container(
                width: 180.0.w,
                decoration: BoxDecoration(
                  color: SdmPalette.white,
                  borderRadius: BorderRadius.circular(10),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
