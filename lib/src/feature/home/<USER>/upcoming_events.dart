import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmRouter;
import 'package:shridattmandir/src/feature/feature.dart'
    show
        AnalyticsCubit,
        CalendarClickEvent,
        HomeCubit,
        HomeState,
        CalendarEventBox;

class UpcomingEvents extends StatelessWidget {
  const UpcomingEvents({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeCubit, HomeState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(
                left: 10.0.w,
                top: 18.0.h,
              ),
              child: Text(
                'UPCOMING EVENTS',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14.sp,
                ),
              ),
            ),
            SizedBox(
              height: 14.h,
            ),
            GestureDetector(
              onTap: () {
                context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                      CalendarClickEvent(),
                    );
                Navigator.pushNamed(
                  context,
                  SdmRouter.calendarEvents,
                );
              },
              child: AspectRatio(
                aspectRatio: 2.8,
                child: ListView.builder(
                  padding: const EdgeInsets.only(
                    left: 10.0,
                    right: 10.0,
                  ).w,
                  scrollDirection: Axis.horizontal,
                  itemCount: state.calendarEvents?.length ?? 0,
                  itemBuilder: (context, count) {
                    final event = state.calendarEvents![count];

                    return CalendarEventBox(
                      eventName: event.title,
                      eventDate: event.start,
                      endDate: event.end,
                      datetime: event.datetime,
                    );
                  },
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
