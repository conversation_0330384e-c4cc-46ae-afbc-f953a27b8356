import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class DrawerListTile extends StatelessWidget {
  const DrawerListTile({super.key, required this.title, required this.onTap});

  final String title;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      minVerticalPadding: 6.w,
      horizontalTitleGap: 16.w,
      title: Text(
        title,
        style: TextStyle(
          fontSize: 14.sp,
        ),
      ),
      onTap: onTap,
    );
  }
}
