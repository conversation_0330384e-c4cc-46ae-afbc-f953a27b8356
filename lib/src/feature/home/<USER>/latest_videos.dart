import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show HomeCubit, HomeState, HomeVideoBox;

class LatestVideos extends StatelessWidget {
  const LatestVideos({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeCubit, HomeState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 20.h,
            ),
            Padding(
              padding: const EdgeInsets.only(
                left: 10.0,
              ).w,
              child: Text(
                'LATEST VIDEOS',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14.sp,
                ),
              ),
            ),
            SizedBox(
              height: 14.h,
            ),
            AspectRatio(
              aspectRatio: 2.8,
              child: ListView.separated(
                padding: const EdgeInsets.only(
                  left: 10.0,
                  right: 10.0,
                ).w,
                scrollDirection: Axis.horizontal,
                itemCount: state.videos?.length ?? 0,
                separatorBuilder: (context, index) {
                  return SizedBox(
                    width: 12.w,
                  );
                },
                itemBuilder: (context, count) {
                  final video = state.videos?[count];
                  if (video == null) {
                    return const SizedBox.shrink();
                  }
                  return HomeVideoBox(
                    videoModel: video,
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }
}
