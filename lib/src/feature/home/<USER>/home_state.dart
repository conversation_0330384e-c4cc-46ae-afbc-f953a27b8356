part of 'home_cubit.dart';

final class HomeState extends Equatable {
  final bool loading;
  final List<CalendarEventModel>? calendarEvents;
  final List<VideoModel>? videos;
  final HomeQuoteModel? homeQuote;

  const HomeState({
    this.loading = false,
    this.calendarEvents,
    this.videos,
    this.homeQuote,
  });

  HomeState copyWith({
    bool? loading,
    List<CalendarEventModel>? calendarEvents,
    List<VideoModel>? videos,
    HomeQuoteModel? homeQuote,
  }) {
    return HomeState(
      loading: loading ?? this.loading,
      calendarEvents: calendarEvents ?? this.calendarEvents,
      videos: videos ?? this.videos,
      homeQuote: homeQuote ?? this.homeQuote,
    );
  }

  @override
  List<Object?> get props => [
        loading,
        calendarEvents,
        videos,
        homeQuote,
      ];
}
