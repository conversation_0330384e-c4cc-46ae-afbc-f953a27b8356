import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/core/theme/sdm_palette.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show HomeCubit, HomeState;
import 'package:shridattmandir/src/shared/shared.dart'
    show SdmPrimaryCta, SdmTextField;

class QuotesInput extends StatelessWidget {
  const QuotesInput({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => HomeCubit(),
      child: BlocBuilder<HomeCubit, HomeState>(
        builder: (context, state) {
          return Scaffold(
            appBar: AppBar(
              title: const Text(
                'Quotes Input',
                style: TextStyle(
                  color: SdmPalette.black,
                  fontWeight: FontWeight.bold,
                ),
              ),
              centerTitle: true,
              iconTheme: const IconThemeData(
                color: SdmPalette.black,
              ),
            ),
            body: Safe<PERSON>rea(
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SdmTextField(
                        maxLines: 5,
                        hintText: 'Enter Quote',
                        controller: context.read<HomeCubit>().quoteController,
                      ),
                      const SizedBox(
                        height: 30,
                      ),
                      SdmPrimaryCta(
                        onPressed: () async {
                          await context.read<HomeCubit>().addHomeQuote();
                        },
                        child: state.loading
                            ? const CircularProgressIndicator.adaptive()
                            : const Padding(
                                padding: EdgeInsets.symmetric(horizontal: 20.0),
                                child: Text(
                                  'Upload Quote',
                                  style: TextStyle(color: SdmPalette.white),
                                ),
                              ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
