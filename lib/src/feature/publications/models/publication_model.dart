import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';

PublicationModel publicationModelFromJson(String str) =>
    PublicationModel.fromJson(json.decode(str));

String publicationModelToJson(PublicationModel data) =>
    json.encode(data.toJson());

class PublicationModel {
  String? title;
  String? image;
  String? description;
  String? url;
  Timestamp? timestamp;

  PublicationModel({
    this.title,
    this.image,
    this.description,
    this.url,
    this.timestamp,
  });

  PublicationModel copyWith({
    String? title,
    String? image,
    String? description,
    String? url,
    Timestamp? timestamp,
  }) =>
      PublicationModel(
        title: title ?? this.title,
        image: image ?? this.image,
        description: description ?? this.description,
        url: url ?? this.url,
        timestamp: timestamp ?? this.timestamp,
      );

  factory PublicationModel.fromJson(Map<String, dynamic> json) =>
      PublicationModel(
        title: json["title"],
        image: json["image"],
        description: json["description"],
        url: json["url"],
        timestamp: json["timestamp"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "image": image,
        "description": description,
        "url": url,
        "timestamp": timestamp,
      };
}
