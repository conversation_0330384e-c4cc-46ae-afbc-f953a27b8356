abstract class PublicationsException implements Exception {
  const PublicationsException(this.error, this.stackTrace);

  final Object error;
  final StackTrace stackTrace;
}

class FetchPublicationsException extends PublicationsException {
  const FetchPublicationsException(super.error, super.stackTrace);
}

class PublicationsDeserializationException extends PublicationsException {
  const PublicationsDeserializationException(super.error, super.stackTrace);
}

class AddPublicationException extends PublicationsException {
  const AddPublicationException(super.error, super.stackTrace);
}
