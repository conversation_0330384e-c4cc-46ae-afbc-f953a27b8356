import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;
import 'package:shridattmandir/src/feature/publications/publications.dart'
    show PublicationsCubit, PublicationsState;
import 'package:shridattmandir/src/shared/shared.dart'
    show SdmTextField, SdmPrimaryCta;

class PublicationsInput extends StatelessWidget {
  const PublicationsInput({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => PublicationsCubit(),
      child: BlocBuilder<PublicationsCubit, PublicationsState>(
        builder: (context, state) {
          return Scaffold(
            appBar: AppBar(
              title: const Text(
                'Publication Input',
                style: TextStyle(
                    color: SdmPalette.black, fontWeight: FontWeight.bold),
              ),
              centerTitle: true,
              iconTheme: const IconThemeData(
                color: SdmPalette.black,
              ),
            ),
            body: Safe<PERSON>rea(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 30.0,
                ),
                child: Center(
                  child: ListView(
                    shrinkWrap: true,
                    children: [
                      SdmTextField(
                        hintText: 'Image URL',
                        controller:
                            context.read<PublicationsCubit>().imageController,
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      SdmTextField(
                        hintText: 'Shop URL',
                        controller:
                            context.read<PublicationsCubit>().urlController,
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      SdmTextField(
                        hintText: 'Title',
                        controller:
                            context.read<PublicationsCubit>().titleController,
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      SdmTextField(
                        hintText: 'Content',
                        controller:
                            context.read<PublicationsCubit>().contentController,
                        maxLines: 10,
                      ),
                      const SizedBox(
                        height: 30,
                      ),
                      Center(
                        child: SdmPrimaryCta(
                          onPressed: () async {
                            await context
                                .read<PublicationsCubit>()
                                .addPublication();
                          },
                          child: state.loading
                              ? const CircularProgressIndicator.adaptive()
                              : const Padding(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 30.0,
                                  ),
                                  child: Text(
                                    'Upload Publication',
                                    style: TextStyle(
                                      color: SdmPalette.white,
                                    ),
                                  ),
                                ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
