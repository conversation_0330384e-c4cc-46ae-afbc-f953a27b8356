import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;
import 'package:shridattmandir/src/feature/feature.dart' show PublicationModel;
import 'package:shridattmandir/src/shared/shared.dart'
    show SdmLaunchUrl, SdmNetworkImage;

class PublicationInfo extends StatelessWidget {
  const PublicationInfo({
    super.key,
    required this.publicationModel,
  });

  final PublicationModel publicationModel;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Publications',
          style: TextStyle(
            color: SdmPalette.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: <Widget>[
          InkWell(
            onTap: () {
              SdmLaunchUrl.sdmLaunchUrl(
                publicationModel.url ?? '',
              );
            },
            child: Center(
              child: Padding(
                padding: const EdgeInsets.only(right: 10),
                child: Text(
                  'BUY NOW',
                  style: TextStyle(
                    color: SdmPalette.textColorGrey,
                    fontWeight: FontWeight.w700,
                    fontSize: 12.sp,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: ListView(
          children: <Widget>[
            Container(
              margin: const EdgeInsets.only(top: 10).h,
              height: 0.38.sh,
              child: SdmNetworkImage(
                url: publicationModel.image,
              ),
            ),
            SizedBox(
              height: 30.h,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 10, right: 10).w,
              child: Text(
                publicationModel.title ?? '',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.w800,
                  fontSize: 22.sp,
                ),
              ),
            ),
            SizedBox(
              height: 10.h,
            ),
            Center(
              child: Padding(
                padding: const EdgeInsets.only(
                  left: 20,
                  right: 20,
                ).w,
                child: Text(
                  publicationModel.description ?? '',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 18.sp,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
