import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;
import 'package:shridattmandir/src/feature/feature.dart'
    show PublicationBox, PublicationsCubit, PublicationsState;
import 'package:shridattmandir/src/shared/shared.dart' show SdmNoContent;

class PublicationsScreen extends StatelessWidget {
  static const String id = 'publications';

  const PublicationsScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Publications',
          style:
              TextStyle(color: SdmPalette.black, fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
      ),
      body: BlocProvider(
        create: (context) => PublicationsCubit()..fetchPublications(),
        child: BlocBuilder<PublicationsCubit, PublicationsState>(
          builder: (context, state) {
            if (state.loading) {
              return const Center(
                child: CircularProgressIndicator.adaptive(),
              );
            }

            if ((state.publications?.length ?? 0) == 0) {
              return Align(
                alignment: Alignment.topCenter,
                child: Padding(
                  padding: const EdgeInsets.only(
                    left: 30.0,
                    right: 30,
                  ),
                  child: SdmNoContent(
                    ctaText: 'Go Back',
                    onCtaTap: () {
                      Navigator.pop(context);
                    },
                  ),
                ),
              );
            }

            return ListView.separated(
              separatorBuilder: (context, index) {
                return SizedBox(
                  height: 20.h,
                );
              },
              padding: EdgeInsets.only(
                left: 24.w,
                right: 24.w,
                bottom: 20.h,
                top: 20.h,
              ),
              itemCount: state.publications?.length ?? 0,
              itemBuilder: (context, index) {
                final publication = state.publications![index];
                return PublicationBox(
                  publicationModel: publication,
                );
              },
            );
          },
        ),
      ),
    );
  }
}
