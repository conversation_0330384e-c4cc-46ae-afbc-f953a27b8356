import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show
        PublicationModel,
        PublicationsDeserializationException,
        AddPublicationException,
        FetchPublicationsException;

class PublicationsRepository {
  const PublicationsRepository(
    FirebaseFirestore firestore,
  ) : _firestore = firestore;

  final FirebaseFirestore _firestore;

  static const String _collectionName = 'publications';
  static const String _orderBy = 'timestamp';

  Future<List<PublicationModel>> fetchPublications() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collectionName)
          .orderBy(_orderBy, descending: true)
          .get();
      final documents = querySnapshot.docs;
      return documents.toPublication();
    } on PublicationsDeserializationException {
      rethrow;
    } on Exception catch (error, stackTrace) {
      throw FetchPublicationsException(error, stackTrace);
    }
  }

  Future<void> addPublication(PublicationModel publication) async {
    try {
      await _firestore.collection(_collectionName).add(
            publication.toJson(),
          );
    } on Exception catch (error, stackTrace) {
      throw AddPublicationException(error, stackTrace);
    }
  }
}

extension on List<QueryDocumentSnapshot> {
  List<PublicationModel> toPublication() {
    final publications = <PublicationModel>[];
    for (final document in this) {
      final data = document.data() as Map<String, dynamic>?;
      if (data != null) {
        try {
          publications.add(PublicationModel.fromJson(data));
        } catch (error, stackTrace) {
          throw PublicationsDeserializationException(error, stackTrace);
        }
      }
    }
    return publications;
  }
}
