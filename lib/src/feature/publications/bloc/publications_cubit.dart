import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/core/core.dart' show CubitExt;
import 'package:shridattmandir/src/feature/feature.dart'
    show PublicationsRepository, PublicationModel;
import 'package:shridattmandir/src/shared/shared.dart' show SdmToast;

part 'publications_state.dart';

class PublicationsCubit extends Cubit<PublicationsState> {
  PublicationsCubit() : super(const PublicationsState());

  final PublicationsRepository _publicationsRepository = PublicationsRepository(
    FirebaseFirestore.instance,
  );

  final TextEditingController imageController = TextEditingController();
  final TextEditingController urlController = TextEditingController();
  final TextEditingController titleController = TextEditingController();
  final TextEditingController contentController = TextEditingController();

  void clearControllers() {
    imageController.clear();
    urlController.clear();
    titleController.clear();
    contentController.clear();
  }

  Future<void> fetchPublications() async {
    safeEmit(
      state.copyWith(
        loading: true,
      ),
    );

    try {
      final publications = await _publicationsRepository.fetchPublications();
      safeEmit(
        state.copyWith(
          publications: publications,
        ),
      );
    } catch (error) {
      SdmToast.show(
        error.toString(),
      );
    }

    safeEmit(
      state.copyWith(
        loading: false,
      ),
    );
  }

  Future<void> addPublication() async {
    if (imageController.text.isEmpty ||
        urlController.text.isEmpty ||
        titleController.text.isEmpty ||
        contentController.text.isEmpty) {
      SdmToast.show(
        'Please fill all the fields',
      );
      return;
    }

    safeEmit(
      state.copyWith(
        loading: true,
      ),
    );

    try {
      final publication = PublicationModel(
        image: imageController.text,
        url: urlController.text,
        title: titleController.text,
        description: contentController.text,
        timestamp: Timestamp.now(),
      );
      await _publicationsRepository.addPublication(publication);
      SdmToast.show(
        'Publication added successfully',
      );

      clearControllers();
    } catch (error) {
      SdmToast.show(
        error.toString(),
      );
    }

    safeEmit(
      state.copyWith(
        loading: false,
      ),
    );
  }

  @override
  Future<void> close() {
    imageController.dispose();
    urlController.dispose();
    titleController.dispose();
    contentController.dispose();
    return super.close();
  }
}
