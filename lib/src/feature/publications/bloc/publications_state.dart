part of 'publications_cubit.dart';

final class PublicationsState extends Equatable {
  const PublicationsState({
    this.loading = false,
    this.publications,
  });

  final bool loading;
  final List<PublicationModel>? publications;

  @override
  List<Object?> get props => [
        loading,
        publications,
      ];

  PublicationsState copyWith({
    bool? loading,
    List<PublicationModel>? publications,
  }) {
    return PublicationsState(
      loading: loading ?? this.loading,
      publications: publications ?? this.publications,
    );
  }
}
