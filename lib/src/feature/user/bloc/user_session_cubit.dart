import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/core/core.dart' show CubitExt;
import 'package:shridattmandir/src/feature/feature.dart' show UserModel;

part 'user_session_state.dart';

/// Manages the current user session state
/// Combines Firebase Auth user with Firestore user profile data
class UserSessionCubit extends Cubit<UserSessionState> {
  UserSessionCubit() : super(const UserSessionState());

  /// Updates the Firebase Auth user
  void updateAuthUser(User? user) {
    safeEmit(
      state.copyWith(
        authUser: () => user,
        isAuthenticated: user != null,
      ),
    );
  }

  /// Updates the user profile data from Firestore
  void updateUserProfile(UserModel? userModel) {
    safeEmit(
      state.copyWith(
        userProfile: () => userModel,
      ),
    );
  }

  /// Updates both auth user and profile data
  void updateUserSession({
    User? authUser,
    UserModel? userProfile,
  }) {
    safeEmit(
      state.copyWith(
        authUser: () => authUser,
        userProfile: () => userProfile,
        isAuthenticated: authUser != null,
      ),
    );
  }

  /// Clears the user session (for logout)
  void clearSession() {
    safeEmit(
      const UserSessionState(),
    );
  }

  /// Gets the current user ID
  String? get currentUserId => state.authUser?.uid;

  /// Checks if user has completed profile
  bool get hasCompletedProfile {
    final profile = state.userProfile;
    return profile?.name != null && profile?.email != null;
  }

  /// Checks if user is admin
  bool get isAdmin => state.userProfile?.isAdmin ?? false;

  /// Checks if user has music premium
  bool get hasMusicPremium => state.userProfile?.hasMusicPremium ?? false;
}
