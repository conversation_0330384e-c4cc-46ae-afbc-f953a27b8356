part of 'user_session_cubit.dart';

final class UserSessionState extends Equatable {
  const UserSessionState({
    this.authUser,
    this.userProfile,
    this.isAuthenticated = false,
  });

  /// Firebase Auth user
  final User? authUser;
  
  /// User profile data from Firestore
  final UserModel? userProfile;
  
  /// Whether user is authenticated
  final bool isAuthenticated;

  UserSessionState copyWith({
    User? Function()? authUser,
    UserModel? Function()? userProfile,
    bool? isAuthenticated,
  }) {
    return UserSessionState(
      authUser: authUser != null ? authUser() : this.authUser,
      userProfile: userProfile != null ? userProfile() : this.userProfile,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
    );
  }

  @override
  List<Object?> get props => [
        authUser,
        userProfile,
        isAuthenticated,
      ];
}
