import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show UserRepository, UserModel, FlavorConfig;

/// Service to coordinate user management operations
/// Handles user creation, updates, and session management
class UserManagementService {
  UserManagementService(this._userRepository);

  final UserRepository _userRepository;

  /// Creates a new user in Firestore after successful authentication
  Future<UserModel> createUserFromAuth(User authUser) async {
    final newUser = UserModel(
      uid: authUser.uid,
      phoneNumber: authUser.phoneNumber,
      name: authUser.displayName,
      email: authUser.email,
      photoUrl: authUser.photoURL,
      isAdmin: false,
    );

    await _userRepository.createUser(user: newUser);
    return newUser;
  }

  /// Gets user data from Firestore
  Future<UserModel?> getUserData(String uid) async {
    return await _userRepository.getUser(uid: uid);
  }

  /// Updates user data in Firestore
  Future<void> updateUserData(UserModel user) async {
    await _userRepository.updateUser(user: user);
  }

  /// Deletes user data from Firestore
  Future<void> deleteUserData(String uid) async {
    await _userRepository.deleteUser(uid: uid);
  }

  /// Sets up user session after authentication
  Future<UserModel> setupUserSession(User authUser) async {
    debugPrint(
        'UserManagementService: Setting up user session for ${authUser.uid}');

    try {
      // Check if user exists in Firestore
      UserModel? existingUser = await getUserData(authUser.uid);

      if (existingUser == null) {
        debugPrint('UserManagementService: Creating new user');
        // Create new user if doesn't exist
        existingUser = await createUserFromAuth(authUser);
      } else {
        debugPrint('UserManagementService: Updating existing user');
        // Update existing user data
        await updateUserData(existingUser);
      }

      // Setup purchases if in production
      try {
        if (FlavorConfig.isProduction()) {
          debugPrint('UserManagementService: Setting up purchases');
          await Purchases.logIn(authUser.uid);
        }
      } catch (e) {
        debugPrint('UserManagementService: Error setting up purchases: $e');
        // Don't fail the entire setup if purchases fail
      }

      debugPrint('UserManagementService: User session setup complete');
      return existingUser;
    } catch (e) {
      debugPrint('UserManagementService: Error setting up user session: $e');
      rethrow;
    }
  }
}
