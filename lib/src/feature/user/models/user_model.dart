class UserModel {
  final String? uid;
  final String? email;
  final String? name;
  final String? photoUrl;
  final String? phoneNumber;
  final bool? isAdmin;
  final List<String>? musicFavorites;
  final String? fcmToken;
  final bool? hasMusicPremium;

  UserModel({
    this.uid,
    this.email,
    this.name,
    this.photoUrl,
    this.phoneNumber,
    this.isAdmin,
    this.musicFavorites,
    this.fcmToken,
    this.hasMusicPremium,
  });

  //from json
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      uid: json['uid'],
      email: json['email'],
      name: json['name'],
      photoUrl: json['photoUrl'],
      phoneNumber: json['phoneNumber'],
      isAdmin: json['isAdmin'],
      musicFavorites: json['musicFavorites'] != null
          ? List<String>.from(json['musicFavorites'])
          : null,
      fcmToken: json['fcmToken'],
      hasMusicPremium: json['hasMusicPremium'],
    );
  }

  //copy with
  UserModel copyWith({
    String? uid,
    String? email,
    String? name,
    String? photoUrl,
    String? phoneNumber,
    bool? isAdmin,
    List<String>? musicFavorites,
    String? fcmToken,
    bool? hasMusicPremium,
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      name: name ?? this.name,
      photoUrl: photoUrl ?? this.photoUrl,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      isAdmin: isAdmin ?? this.isAdmin,
      musicFavorites: musicFavorites ?? this.musicFavorites,
      fcmToken: fcmToken ?? this.fcmToken,
      hasMusicPremium: hasMusicPremium ?? this.hasMusicPremium,
    );
  }

  //to json
  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'email': email,
      'name': name,
      'photoUrl': photoUrl,
      'phoneNumber': phoneNumber,
      'isAdmin': isAdmin,
      'musicFavorites': musicFavorites,
      'fcmToken': fcmToken,
      'hasMusicPremium': hasMusicPremium,
    };
  }
}
