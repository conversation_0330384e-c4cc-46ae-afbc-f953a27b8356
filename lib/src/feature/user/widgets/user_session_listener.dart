import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/feature/auth/presentation/bloc/auth_bloc.dart';
import 'package:shridattmandir/src/feature/auth/presentation/bloc/auth_state.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show UserSessionCubit, UserManagementService, UserRepository;

/// Widget that listens to auth state changes and coordinates user session
class UserSessionListener extends StatelessWidget {
  const UserSessionListener({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listenWhen: (previous, current) => previous.user != current.user,
      listener: (context, authState) async {
        final userSessionCubit = context.read<UserSessionCubit>();
        final userRepository = context.read<UserRepository>();
        final userManagementService = UserManagementService(userRepository);

        if (authState.user != null) {
          // User signed in - setup user session
          try {
            // Convert AuthUser to Firebase User for UserManagementService
            // We need to get the Firebase User from FirebaseAuth
            final firebaseUser = FirebaseAuth.instance.currentUser;
            if (firebaseUser != null) {
              final userModel =
                  await userManagementService.setupUserSession(firebaseUser);
              userSessionCubit.updateUserSession(
                authUser: firebaseUser,
                userProfile: userModel,
              );
            }
          } catch (e) {
            // Handle error
            debugPrint('Error setting up user session: $e');
          }
        } else {
          // User signed out - clear session
          userSessionCubit.clearSession();
        }
      },
      child: child,
    );
  }
}
