import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show
        CalendarEventModel,
        CalendarEventsDeserializationException,
        FetchCalendarEventsException;

class CalendarEventsRepository {
  const CalendarEventsRepository(
    FirebaseFirestore firestore,
  ) : _firestore = firestore;

  final FirebaseFirestore _firestore;

  static const String _collectionName = 'events';
  static const String _orderBy = 'datetime';
  static const int _limit = 15;

  Future<List<CalendarEventModel>> fetchCalendarEvents() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collectionName)
          .orderBy(
            _orderBy,
            descending: true,
          )
          .limit(_limit)
          .get();
      final documents = querySnapshot.docs;
      return documents.toCalendarEvent();
    } on CalendarEventsDeserializationException {
      rethrow;
    } on Exception catch (error, stackTrace) {
      throw FetchCalendarEventsException(error, stackTrace);
    }
  }
}

extension on List<QueryDocumentSnapshot> {
  List<CalendarEventModel> toCalendarEvent() {
    final calendarEvents = <CalendarEventModel>[];
    for (final document in this) {
      final data = document.data() as Map<String, dynamic>?;
      if (data != null) {
        try {
          calendarEvents.add(CalendarEventModel.fromJson(data));
        } catch (error, stackTrace) {
          throw CalendarEventsDeserializationException(error, stackTrace);
        }
      }
    }
    return calendarEvents;
  }
}
