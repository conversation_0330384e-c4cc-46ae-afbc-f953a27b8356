part of 'calendar_events_cubit.dart';

final class CalendarEventsState extends Equatable {
  const CalendarEventsState({
    this.calendarEvents,
    this.loading = false,
  });

  final List<CalendarEventModel>? calendarEvents;
  final bool loading;

  @override
  List<Object?> get props => [
        calendarEvents,
        loading,
      ];

  CalendarEventsState copyWith({
    List<CalendarEventModel>? calendarEvents,
    bool? loading,
  }) {
    return CalendarEventsState(
      calendarEvents: calendarEvents ?? this.calendarEvents,
      loading: loading ?? this.loading,
    );
  }
}
