import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/core/core.dart' show CubitExt;
import 'package:shridattmandir/src/feature/feature.dart'
    show CalendarEventsRepository, CalendarEventModel;
import 'package:shridattmandir/src/shared/shared.dart' show SdmToast;

part 'calendar_events_state.dart';

class CalendarEventsCubit extends Cubit<CalendarEventsState> {
  CalendarEventsCubit() : super(const CalendarEventsState());

  final CalendarEventsRepository _calendarEventsRepository =
      CalendarEventsRepository(
    FirebaseFirestore.instance,
  );

  Future<void> fetchCalendarEvents() async {
    safeEmit(
      state.copyWith(
        loading: true,
      ),
    );
    try {
      final calendarEvents =
          await _calendarEventsRepository.fetchCalendarEvents();

      final upcomingEvents = calendarEvents.where(
        (event) {
          if (event.datetime == null) return false;

          return event.datetime!.isAfter(
            DateTime.now(),
          );
        },
      ).toList();

      safeEmit(
        state.copyWith(
          calendarEvents: upcomingEvents.reversed.toList(),
        ),
      );
    } catch (error) {
      SdmToast.show(
        error.toString(),
      );
    }
    safeEmit(
      state.copyWith(
        loading: false,
      ),
    );
  }
}
