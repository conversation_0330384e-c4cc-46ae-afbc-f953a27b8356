import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmUrls, SdmPalette;
import 'package:shridattmandir/src/feature/feature.dart'
    show CalendarEventsCubit, CalendarEventsState, CalendarEventBox;
import 'package:shridattmandir/src/shared/shared.dart'
    show SdmLaunchUrl, SdmNoContent;

class CalendarEventsScreen extends StatelessWidget {
  static const String id = 'events';

  const CalendarEventsScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Events',
          style: TextStyle(
            color: SdmPalette.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: <Widget>[
          InkWell(
            onTap: () async {
              await SdmLaunchUrl.sdmLaunchUrl(
                SdmUrls.kCalendar,
              );
            },
            child: Center(
              child: Padding(
                padding: const EdgeInsets.only(right: 10),
                child: Text(
                  'Calendar',
                  style: TextStyle(
                    color: SdmPalette.textColorGrey,
                    fontWeight: FontWeight.bold,
                    fontSize: 12.sp,
                  ),
                ),
              ),
            ),
          )
        ],
      ),
      body: BlocProvider(
        create: (context) => CalendarEventsCubit()..fetchCalendarEvents(),
        child: BlocBuilder<CalendarEventsCubit, CalendarEventsState>(
          builder: (context, state) {
            if (state.loading) {
              return const Center(
                child: CircularProgressIndicator.adaptive(),
              );
            }

            if ((state.calendarEvents?.length ?? 0) == 0) {
              return Align(
                alignment: Alignment.topCenter,
                child: Padding(
                  padding: const EdgeInsets.only(
                    left: 30.0,
                    right: 30,
                  ),
                  child: SdmNoContent(
                    ctaText: 'Go Back',
                    onCtaTap: () {
                      Navigator.pop(context);
                    },
                  ),
                ),
              );
            }

            return GridView.count(
              crossAxisCount: 2,
              padding: const EdgeInsets.symmetric(
                horizontal: 10.0,
                vertical: 20.0,
              ),
              children: [
                for (var event in state.calendarEvents ?? [])
                  CalendarEventBox(
                    eventDate: event.start,
                    eventName: event.title,
                    datetime: event.datetime,
                    endDate: event.end,
                  ),
              ],
            );
          },
        ),
      ),
    );
  }
}
