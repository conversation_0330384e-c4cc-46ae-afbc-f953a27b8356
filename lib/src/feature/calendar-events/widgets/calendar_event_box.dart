import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show Assets, SdmPalette;

class CalendarEventBox extends StatelessWidget {
  const CalendarEventBox({
    super.key,
    required this.eventName,
    required this.eventDate,
    this.endDate,
    this.datetime,
  });

  final String? eventName;
  final String? eventDate;
  final String? endDate;
  final DateTime? datetime;

  @override
  Widget build(BuildContext context) {
    String? text;
    if (endDate == null || endDate == eventDate) {
      text = eventDate;
    } else {
      text = '$eventDate to $endDate';
    }
    return Stack(
      children: [
        Align(
          alignment: Alignment.center,
          child: SizedBox(
            width: 150.0.w,
            child: Assets.sdmImages.eventCal.image(
              fit: BoxFit.contain,
            ),
          ),
        ),
        Positioned.fill(
          top: 30.h,
          child: Align(
            alignment: Alignment.bottomCenter,
            child: SizedBox(
              width: 120.w,
              child: Center(
                child: ListView(
                  padding: const EdgeInsets.only(top: 0),
                  shrinkWrap: true,
                  physics: const ClampingScrollPhysics(),
                  children: [
                    SizedBox(
                      height: 10.h,
                    ),
                    Text(
                      eventName ?? '',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 15.5.sp,
                        color: SdmPalette.lightRed,
                      ),
                    ),
                    SizedBox(
                      height: 5.h,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(bottom: 10).h,
                      child: Text(
                        text!,
                        style: TextStyle(
                          color: SdmPalette.textColorGrey,
                          fontSize: 14.sp,
                        ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.clip,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
