abstract class CalendarEventsException implements Exception {
  const CalendarEventsException(this.error, this.stackTrace);

  final Object error;
  final StackTrace stackTrace;
}

class FetchCalendarEventsException extends CalendarEventsException {
  const FetchCalendarEventsException(super.error, super.stackTrace);
}

class CalendarEventsDeserializationException extends CalendarEventsException {
  const CalendarEventsDeserializationException(super.error, super.stackTrace);
}
