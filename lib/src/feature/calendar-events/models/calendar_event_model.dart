import 'dart:convert';

CalendarEventModel calendarEventModelFromJson(String str) =>
    CalendarEventModel.fromJson(json.decode(str));

String calendarEventModelToJson(CalendarEventModel data) =>
    json.encode(data.toJson());

class CalendarEventModel {
  String? start;
  String? etag;
  String? end;
  String? title;
  DateTime? datetime;
  DateTime? datetimeStart;

  CalendarEventModel({
    this.start,
    this.etag,
    this.end,
    this.title,
    this.datetime,
    this.datetimeStart,
  });

  CalendarEventModel copyWith({
    String? start,
    String? etag,
    String? end,
    String? title,
    DateTime? datetime,
    DateTime? datetimeStart,
  }) =>
      CalendarEventModel(
        start: start ?? this.start,
        etag: etag ?? this.etag,
        end: end ?? this.end,
        title: title ?? this.title,
        datetime: datetime ?? this.datetime,
        datetimeStart: datetimeStart ?? this.datetimeStart,
      );

  factory CalendarEventModel.from<PERSON>son(Map<String, dynamic> json) =>
      CalendarEventModel(
        start: json["start"],
        etag: json["etag"],
        end: json["end"],
        title: json["title"],
        datetime:
            json["datetime"] == null ? null : DateTime.parse(json["datetime"]),
        datetimeStart: json["datetimeStart"] == null
            ? null
            : DateTime.parse(json["datetimeStart"]),
      );

  Map<String, dynamic> toJson() => {
        "start": start,
        "etag": etag,
        "end": end,
        "title": title,
        "datetime": datetime?.toIso8601String(),
        "datetimeStart": datetimeStart?.toIso8601String(),
      };
}
