import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show AnalyticsCubit, MusicPaywallSheetOpenEvent, Paywall;

/// Centralized manager for displaying paywalls consistently across the app
/// This ensures a uniform user experience and consistent analytics tracking
class PaywallManager {
  PaywallManager._();

  /// Shows the paywall as a modal bottom sheet
  /// This is the preferred method for displaying the paywall
  static Future<void> showPaywallModal({
    required BuildContext context,
    bool trackAnalytics = true,
  }) async {
    if (trackAnalytics) {
      context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
            MusicPaywallSheetOpenEvent(),
          );
    }

    await showModalBottomSheet(
      context: context,
      useRootNavigator: true,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: const Radius.circular(10.0).r,
        ),
      ),
      constraints: BoxConstraints(
        maxWidth: 0.9.sw,
        maxHeight: 0.85.sh,
      ),
      builder: (context) => const Paywall(),
    );
  }
}
