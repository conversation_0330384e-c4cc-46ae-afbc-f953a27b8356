import 'package:purchases_flutter/purchases_flutter.dart';

final class PurchasesRepository {
  Future<void> configure(PurchasesConfiguration configuration) async {
    await Purchases.configure(configuration);
  }

  Future<Offerings> getOfferings() async {
    return await Purchases.getOfferings();
  }

  Future<CustomerInfo> purchasePackage(Package package) async {
    return await Purchases.purchasePackage(package);
  }

  Future<CustomerInfo> getCustomerInfo() async {
    return await Purchases.getCustomerInfo();
  }

  Future<CustomerInfo> restorePurchase() async {
    return await Purchases.restorePurchases();
  }

  Future<CustomerInfo> logout() async {
    return await Purchases.logOut();
  }

  Future<LogInResult> login(String appUserID) async {
    return await Purchases.logIn(appUserID);
  }

  Future<void> setAttributes(Map<String, String> attributes) async {
    await Purchases.setAttributes(attributes);
  }

  Future<void> setLogLevel(LogLevel logLevel) async {
    await Purchases.setLogLevel(logLevel);
  }

  Future<bool> isAnonymous() async {
    return await Purchases.isAnonymous;
  }

  Future<void> isConfigured() async {
    await Purchases.isConfigured;
  }

  Future<void> collectDeviceIdentifiers() async {
    await Purchases.collectDeviceIdentifiers();
  }
}
