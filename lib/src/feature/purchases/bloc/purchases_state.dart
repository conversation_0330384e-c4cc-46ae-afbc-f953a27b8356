part of 'purchases_cubit.dart';

final class PurchasesState extends Equatable {
  const PurchasesState({
    this.loading = false,
    this.offerings,
    this.customerInfo,
    this.hasMusicPremium = false,
    this.stateChange = false,
  });

  final bool loading;
  final Offerings? offerings;
  final CustomerInfo? customerInfo;
  final bool hasMusicPremium;
  final bool stateChange;

  PurchasesState copyWith({
    bool? loading,
    Offerings? Function()? offerings,
    CustomerInfo? Function()? customerInfo,
    bool? hasMusicPremium,
    bool? stateChange,
  }) {
    return PurchasesState(
      loading: loading ?? this.loading,
      offerings: offerings != null ? offerings() : this.offerings,
      customerInfo: customerInfo != null ? customerInfo() : this.customerInfo,
      hasMusicPremium: hasMusicPremium ?? this.hasMusicPremium,
      stateChange: stateChange ?? this.stateChange,
    );
  }

  @override
  List<Object?> get props => [
        loading,
        offerings,
        customerInfo,
        hasMusicPremium,
        stateChange,
      ];
}
