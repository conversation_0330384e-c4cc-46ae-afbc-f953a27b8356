import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:shridattmandir/src/core/core.dart' show CubitExt;
import 'package:shridattmandir/src/feature/feature.dart'
    show
        FlavorConfig,
        PurchasesRepository,
        kApplePurchaseApiKey,
        kEntitlementId,
        kGooglePurchaseApiKey;
import 'package:shridattmandir/src/shared/shared.dart' show SdmToast;

part 'purchases_state.dart';

class PurchasesCubit extends Cubit<PurchasesState> {
  PurchasesCubit({
    required FirebaseAuth firebaseAuth,
    required PurchasesRepository purchasesRepository,
    required FirebaseMessaging firebaseMessaging,
    required DeviceInfoPlugin deviceInfoPlugin,
  })  : _firebaseAuth = firebaseAuth,
        _purchasesRepository = purchasesRepository,
        _firebaseMessaging = firebaseMessaging,
        _deviceInfoPlugin = deviceInfoPlugin,
        super(const PurchasesState());

  final FirebaseAuth _firebaseAuth;
  final PurchasesRepository _purchasesRepository;
  final FirebaseMessaging _firebaseMessaging;
  final DeviceInfoPlugin _deviceInfoPlugin;

  Future<void> initPlatformState() async {
    if (FlavorConfig.isDevelopment()) return;
    await _purchasesRepository.setLogLevel(LogLevel.error);

    late PurchasesConfiguration configuration;

    if (Platform.isAndroid) {
      configuration = PurchasesConfiguration(kGooglePurchaseApiKey);
    } else if (Platform.isIOS) {
      configuration = PurchasesConfiguration(kApplePurchaseApiKey);
    }
    await _purchasesRepository.configure(configuration);
    subscriptionChangeListener();
  }

  Future<void> fetchOfferings() async {
    if (FlavorConfig.isDevelopment()) return;
    try {
      final offerings = await _purchasesRepository.getOfferings();

      if (offerings.current != null) {
        safeEmit(
          state.copyWith(
            offerings: () => offerings,
          ),
        );
      }
    } on PlatformException catch (e) {
      SdmToast.show(e.message ?? 'Something went wrong');
    }
  }

  Future<void> purchaseOffering(Package package) async {
    if (FlavorConfig.isDevelopment()) return;

    safeEmit(
      state.copyWith(
        loading: true,
      ),
    );

    try {
      CustomerInfo customerInfo =
          await _purchasesRepository.purchasePackage(package);
      bool hasMusicPremium =
          customerInfo.entitlements.all[kEntitlementId]?.isActive ?? false;
      safeEmit(
        state.copyWith(
          customerInfo: () => customerInfo,
          hasMusicPremium: hasMusicPremium,
        ),
      );
    } on PlatformException catch (e) {
      SdmToast.show(e.message ?? 'Something went wrong');
    }

    safeEmit(
      state.copyWith(
        loading: false,
      ),
    );
  }

  Future<void> getSubscriptionStatus() async {
    if (FlavorConfig.isDevelopment()) return;
    try {
      CustomerInfo customerInfo = await _purchasesRepository.getCustomerInfo();
      bool hasMusicPremium =
          customerInfo.entitlements.all[kEntitlementId]?.isActive ?? false;
      safeEmit(
        state.copyWith(
          customerInfo: () => customerInfo,
          hasMusicPremium: hasMusicPremium,
        ),
      );
    } on PlatformException catch (e) {
      SdmToast.show(e.message ?? 'Something went wrong');
    }
  }

  Future<void> restorePurchase() async {
    if (FlavorConfig.isDevelopment()) return;
    try {
      CustomerInfo customerInfo = await _purchasesRepository.restorePurchase();

      bool hasMusicPremium =
          customerInfo.entitlements.all[kEntitlementId]?.isActive ?? false;
      safeEmit(
        state.copyWith(
          customerInfo: () => customerInfo,
          hasMusicPremium: hasMusicPremium,
        ),
      );
    } on PlatformException catch (e) {
      SdmToast.show(e.message ?? 'Something went wrong');
    }
  }

  Future<void> logOut() async {
    if (FlavorConfig.isDevelopment()) return;

    if (await _purchasesRepository.isAnonymous()) {
      await _purchasesRepository.logout();
    }
    safeEmit(
      state.copyWith(
        customerInfo: () => null,
        hasMusicPremium: false,
      ),
    );
  }

  void subscriptionChangeListener() {
    if (FlavorConfig.isDevelopment()) return;
    Purchases.addCustomerInfoUpdateListener(
      (customerInfo) {
        safeEmit(
          state.copyWith(
            customerInfo: () => customerInfo,
            hasMusicPremium:
                customerInfo.entitlements.all[kEntitlementId]?.isActive ??
                    false,
          ),
        );
      },
    );
  }

  Future<void> purchaseFlow() async {
    if (FlavorConfig.isDevelopment()) return;
    if (await _purchasesRepository.isAnonymous()) {
      await _purchasesRepository
          .login(FirebaseAuth.instance.currentUser?.uid ?? '');
    }

    await fetchOfferings();
    await getSubscriptionStatus();

    safeEmit(
      state.copyWith(
        stateChange: !state.stateChange,
      ),
    );
  }

  Future<void> setAttributes() async {
    if (FlavorConfig.isDevelopment()) return;

    final token = await _firebaseMessaging.getToken();
    final apnsToken = await _firebaseMessaging.getAPNSToken();
    final androidInfo =
        Platform.isAndroid ? await _deviceInfoPlugin.androidInfo : null;
    final iosInfo = Platform.isIOS ? await _deviceInfoPlugin.iosInfo : null;

    await _purchasesRepository.setAttributes(
      {
        '\$email': _firebaseAuth.currentUser?.email ?? '',
        '\$phoneNumber': _firebaseAuth.currentUser?.phoneNumber ?? '',
        '\$fcmTokens': token ?? '',
        '\$apnsTokens': apnsToken ?? '',
        '\$displayName': _firebaseAuth.currentUser?.displayName ?? '',
        '\$androidId': androidInfo?.id ?? '',
        '\$idfv': iosInfo?.identifierForVendor ?? '',
      },
    );
  }

  String getSubscriptionPeriod(String iso8061String) {
    //For example, P1W equates to one week, P1M equates to one month, P3M equates to three months, P6M equates to six months, and P1Y equates to one year.

    final period = iso8061String.substring(1, 2);
    final periodType = iso8061String.substring(2, 3);

    switch (periodType) {
      case 'W':
        return '$period week';
      case 'M':
        return '$period month';
      case 'Y':
        return '$period year';
      default:
        return '';
    }
  }
}
