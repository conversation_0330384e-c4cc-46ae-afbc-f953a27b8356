# Subscription Paywall Implementation Guide

This document outlines the consistent and maintainable approach for implementing subscription paywalls across the app.

## Architecture Overview

The subscription system follows a centralized approach with two main components:

1. **SubscriptionService** - Centralized subscription state management
2. **PaywallManager** - Consistent paywall display logic

## Components

### 1. SubscriptionService

Provides a consistent interface for subscription checks across the app.

```dart
// Get subscription service from PurchasesCubit
final subscriptionService = SubscriptionService(
  purchasesCubit: context.read<PurchasesCubit>(),
);

// Check subscription status
if (subscriptionService.hasMusicPremium) {
  // User has premium access
}

// Check with admin privileges
if (subscriptionService.hasMusicAccess(isAdmin: user.isAdmin)) {
  // User has access (premium or admin)
}
```

### 2. PaywallManager

Centralized paywall display with consistent UI and analytics.

```dart
// Show paywall as modal (only method available)
await PaywallManager.showPaywallModal(context: context);
```

### 3. Manual Subscription Checks

For cases where you need manual subscription checks in your business logic:

```dart
// In navigation or business logic
final subscriptionService = SubscriptionService(
  purchasesCubit: context.read<PurchasesCubit>(),
);

if (subscriptionService.hasMusicAccess(isAdmin: user.isAdmin)) {
  // User has access - proceed with premium feature
  Navigator.pushNamed(context, SdmRouter.musicList);
} else {
  // Show paywall
  await PaywallManager.showPaywallModal(context: context);
}
```

## Usage Patterns

### 1. Navigation-Based Premium Checks

```dart
// In drawer or navigation
DrawerListTile(
  title: 'Music',
  onTap: () async {
    context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
      MusicClickEvent(),
    );

    final subscriptionService = SubscriptionService(
      purchasesCubit: context.read<PurchasesCubit>(),
    );

    if (subscriptionService.hasMusicAccess(isAdmin: user.isAdmin)) {
      Navigator.popAndPushNamed(context, SdmRouter.musicList);
      return;
    }

    if (context.mounted) {
      await PaywallManager.showPaywallModal(context: context);
    }
  },
)
```

### 2. Conditional Feature Access

```dart
// In build methods
BlocBuilder<PurchasesCubit, PurchasesState>(
  builder: (context, state) {
    final subscriptionService = SubscriptionService(
      purchasesCubit: context.read<PurchasesCubit>(),
    );

    if (subscriptionService.hasMusicAccess(isAdmin: user.isAdmin)) {
      return MusicFeatures();
    }

    return GestureDetector(
      onTap: () => PaywallManager.showPaywallModal(context: context),
      child: PremiumRequiredWidget(),
    );
  },
)
```

## Best Practices

### ✅ DO

- Use `SubscriptionService` for all subscription checks
- Use `PaywallManager` for consistent paywall display
- Track analytics when showing paywalls
- Handle development mode appropriately
- Use the cubit pattern for state management
- Keep UI simple and maintain original user experience

### ❌ DON'T

- Create new `PurchasesRepository()` instances directly
- Bypass the cubit for subscription checks
- Implement custom paywall display logic
- Forget to track analytics
- Mix subscription logic with business logic
- Overcomplicate the UI with unnecessary guards or custom screens

## Migration Guide

### From Direct Repository Usage

```dart
// OLD - Direct repository usage
final customerInfo = await PurchasesRepository().getCustomerInfo();
final hasPremium = customerInfo.entitlements.all[kEntitlementId]?.isActive ?? false;

// NEW - Use SubscriptionService
final subscriptionService = SubscriptionService(
  purchasesCubit: context.read<PurchasesCubit>(),
);
final hasPremium = subscriptionService.hasMusicPremium;
```

### From Custom Paywall Display

```dart
// OLD - Custom modal
showModalBottomSheet(
  context: context,
  builder: (context) => const Paywall(),
);

// NEW - Use PaywallManager
PaywallManager.showPaywallModal(context: context);
```

### From Manual Premium Checks

```dart
// OLD - Manual checks scattered everywhere
if (purchasesState.hasMusicPremium || user.isAdmin || FlavorConfig.isDevelopment()) {
  Navigator.pushNamed(context, SdmRouter.musicList);
} else {
  showModalBottomSheet(/* custom paywall logic */);
}

// NEW - Use SubscriptionService
final subscriptionService = SubscriptionService(
  purchasesCubit: context.read<PurchasesCubit>(),
);

if (subscriptionService.hasMusicAccess(isAdmin: user.isAdmin)) {
  Navigator.pushNamed(context, SdmRouter.musicList);
} else {
  await PaywallManager.showPaywallModal(context: context);
}
```

## Error Handling

The new system includes built-in error handling:

- Development mode bypass for testing
- Graceful fallbacks for network errors
- Consistent error messaging
- Analytics tracking for error scenarios

## Testing

```dart
// Mock subscription service for tests
final mockSubscriptionService = MockSubscriptionService();
when(mockSubscriptionService.hasMusicPremium).thenReturn(true);

// Test premium feature guards
testWidgets('shows premium content when subscribed', (tester) async {
  // Setup mock cubit with premium access
  // Test widget behavior
});
```

This approach ensures consistency, maintainability, and reliability across the entire subscription system.
