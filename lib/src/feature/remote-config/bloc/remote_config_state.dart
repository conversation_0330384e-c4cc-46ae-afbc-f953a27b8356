part of 'remote_config_cubit.dart';

final class RemoteConfigState extends Equatable {
  const RemoteConfigState({
    this.remoteConfig = const <RemoteConfigEnum, dynamic>{},
    this.categories = const <String>[],
    this.referenceBooks = const <String>[],
    this.homeImages = const <String>[],
    this.autoPlayLimit = 0,
  });

  final Map<RemoteConfigEnum, dynamic> remoteConfig;
  final List<String> categories;
  final List<String> referenceBooks;
  final List<String> homeImages;
  final int autoPlayLimit;

  RemoteConfigState copyWith({
    Map<RemoteConfigEnum, dynamic>? remoteConfig,
    List<String>? categories,
    List<String>? referenceBooks,
    List<String>? homeImages,
    int? autoPlayLimit,
  }) {
    return RemoteConfigState(
      remoteConfig: remoteConfig ?? this.remoteConfig,
      categories: categories ?? this.categories,
      referenceBooks: referenceBooks ?? this.referenceBooks,
      homeImages: homeImages ?? this.homeImages,
      autoPlayLimit: autoPlayLimit ?? this.autoPlayLimit,
    );
  }

  @override
  List<Object?> get props => [
        remoteConfig,
        categories,
        referenceBooks,
        homeImages,
        autoPlayLimit,
      ];
}
