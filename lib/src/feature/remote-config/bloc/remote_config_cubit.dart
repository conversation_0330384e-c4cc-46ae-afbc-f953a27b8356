import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/core/core.dart' show CubitExt;
import 'package:shridattmandir/src/feature/feature.dart'
    show FlavorConfig, RemoteConfigEnum;
import 'package:shridattmandir/src/shared/shared.dart' show SdmToast;

part 'remote_config_state.dart';

class RemoteConfigCubit extends Cubit<RemoteConfigState> {
  RemoteConfigCubit()
      : super(
          RemoteConfigState(
            remoteConfig: {
              RemoteConfigEnum.categories: json.decode(
                '''
            {
  "categories": [
    "अभंग",
    "विरहीणी",
    "रूपके",
    "पाळणे",
    "पोवाडे",
    "आरती",
    "दृष्ट",
    "उपकार",
    "उपदेश",
    "धावा",
    "नाम",
    "गौळण",
    "भारूड",
    "जोगवा",
    "काकड आरती",
    "काल्याची पदे"
  ]
}
            ''',
              ),
              RemoteConfigEnum.referenceBooks: json.decode(
                '''
            {
  "referenceBooks": [
    "भजनपुष्प",
    "भजनांजली",
    "दत्ताभागीरथी ओघ"
  ]
}
            ''',
              ),
              RemoteConfigEnum.homeImages: json.decode('''
{
  "homeImages": [
    "https://firebasestorage.googleapis.com/v0/b/shri-datt-mandir-f0e60.appspot.com/o/assets%2Fhome%2Fbagan-scene-myanmar%201.png?alt=media&token=739609fc-1dd8-41f6-aaa3-3f93b80e187f",
    "https://firebasestorage.googleapis.com/v0/b/shri-datt-mandir-f0e60.appspot.com/o/assets%2Fhome%2Fbeautiful-landscape-sea-ocean-leisure-travel-vacation%201.png?alt=media&token=a10e0e70-6285-4834-b8c9-cf7ebca962d2",
    "https://firebasestorage.googleapis.com/v0/b/shri-datt-mandir-f0e60.appspot.com/o/assets%2Fhome%2Fphoto-of-mountain-during-dawn-3224156.png?alt=media&token=d089d00e-c2e8-4cec-8653-8cc3d10cfa19",
    "https://firebasestorage.googleapis.com/v0/b/shri-datt-mandir-f0e60.appspot.com/o/assets%2Fhome%2Fscenery-wood-rocky-fingers-dream%201.png?alt=media&token=e01b27de-cd68-43c6-8a9f-8134c14cb31f",
    "https://firebasestorage.googleapis.com/v0/b/shri-datt-mandir-f0e60.appspot.com/o/assets%2Fhome%2Ftravelling-iran%201.png?alt=media&token=d4218f21-2354-4ecc-a0bb-307dbe11f06f",
    "https://firebasestorage.googleapis.com/v0/b/shri-datt-mandir-f0e60.appspot.com/o/assets%2Fhome%2Fyoung-woman-meditating-from-mountain-top-facing-beautiful-sunset-healthy-mind-body%201.png?alt=media&token=9c04a582-9311-44a5-83be-fbc35a0832ce"
  ]
}
'''),
              RemoteConfigEnum.autoPlayLimit: 10,
            },
          ),
        );

  final FirebaseRemoteConfig _remoteConfig = FirebaseRemoteConfig.instance;

  Future<void> fetchAndActivate() async {
    await _remoteConfig.setConfigSettings(
      RemoteConfigSettings(
        fetchTimeout: const Duration(minutes: 1),
        minimumFetchInterval: Duration(
          seconds: FlavorConfig.isDevelopment() ? 1 : 300,
        ),
      ),
    );
    await _remoteConfig.fetchAndActivate();

    try {
      Map<RemoteConfigEnum, dynamic> remoteConfigMap = {
        RemoteConfigEnum.categories: jsonDecode(
          _remoteConfig.getString(
            RemoteConfigEnum.categories.name,
          ),
        ),
        RemoteConfigEnum.referenceBooks: jsonDecode(
          _remoteConfig.getString(
            RemoteConfigEnum.referenceBooks.name,
          ),
        ),
        RemoteConfigEnum.homeImages: jsonDecode(
          _remoteConfig.getString(
            RemoteConfigEnum.homeImages.name,
          ),
        ),
        RemoteConfigEnum.autoPlayLimit: _remoteConfig.getInt(
          RemoteConfigEnum.autoPlayLimit.name,
        ),
      };

      safeEmit(
        state.copyWith(
          remoteConfig: remoteConfigMap,
          categories: remoteConfigMap[RemoteConfigEnum.categories]['categories']
              .cast<String>(),
          referenceBooks: remoteConfigMap[RemoteConfigEnum.referenceBooks]
                  ['referenceBooks']
              .cast<String>(),
          homeImages: remoteConfigMap[RemoteConfigEnum.homeImages]['homeImages']
              .cast<String>(),
          autoPlayLimit: remoteConfigMap[RemoteConfigEnum.autoPlayLimit],
        ),
      );
    } catch (e) {
      SdmToast.show(
        e.toString(),
      );
    }
  }
}
