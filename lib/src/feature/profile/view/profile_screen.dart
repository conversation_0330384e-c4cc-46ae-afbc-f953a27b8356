import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette, SdmRouter;
import 'package:shridattmandir/src/feature/feature.dart'
    show
        AuthCubit,
        MusicPlayerCubit,
        ProfileCubit,
        ProfileState,
        PurchasesCubit,
        UserSessionCubit,
        UserRepository;
import 'package:shridattmandir/src/feature/profile/widget/adaptive_dialog.dart';
import 'package:shridattmandir/src/shared/shared.dart'
    show SdmTextField, SdmPrimaryCta;

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileCubit, ProfileState>(
      bloc: BlocProvider.of<ProfileCubit>(context)
        ..getUserData(
          uid: context.read<UserSessionCubit>().state.authUser?.uid ?? '',
        ),
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(
            title: const Text(
              'Profile',
            ),
            foregroundColor: SdmPalette.black,
            actions: [
              IconButton(
                onPressed: () async {
                  final shouldLogout = await showDialog<bool>(
                    context: context,
                    builder: (context) => const AdaptiveDialog(
                      title: 'Logout',
                      content: 'Are you sure you want to logout?',
                      confirmText: 'Logout',
                      isDestructive: true,
                    ),
                  );

                  if (shouldLogout == true && context.mounted) {
                    final musicCubit = context.read<MusicPlayerCubit>();
                    final purchasesCubit = context.read<PurchasesCubit>();
                    final authCubit = context.read<AuthCubit>();
                    final userSessionCubit = context.read<UserSessionCubit>();

                    await musicCubit.stop();
                    await purchasesCubit.logOut();
                    userSessionCubit.clearSession();
                    await authCubit.signOut();

                    // Navigate to auth screen
                    if (context.mounted) {
                      Navigator.of(context).pushNamedAndRemoveUntil(
                        SdmRouter.phoneRegistration,
                        (route) => false,
                      );
                    }
                  }
                },
                icon: const Icon(
                  Icons.logout,
                  color: SdmPalette.lightRed,
                ),
              ),
            ],
          ),
          body: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 24.0,
            ),
            child: Column(
              children: [
                Expanded(
                  child: ListView(
                    children: [
                      SizedBox(
                        height: 50.h,
                      ),
                      SdmTextField(
                        hintText: 'Name',
                        fontSize: 14.sp,
                        controller: context.read<ProfileCubit>().nameController,
                      ),
                      SizedBox(
                        height: 34.h,
                      ),
                      SdmTextField(
                        hintText: 'Email',
                        fontSize: 14.sp,
                        controller:
                            context.read<ProfileCubit>().emailController,
                      ),
                      SizedBox(
                        height: 34.h,
                      ),
                      SdmTextField(
                        hintText: 'Phone',
                        fontSize: 14.sp,
                        enabled: false,
                        controller:
                            context.read<ProfileCubit>().phoneController,
                      ),

                      //save button
                      SizedBox(
                        height: 40.h,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SdmPrimaryCta(
                            onPressed: () {
                              context.read<ProfileCubit>().setUserData(
                                    uid: context
                                            .read<UserSessionCubit>()
                                            .state
                                            .authUser
                                            ?.uid ??
                                        '',
                                  );
                              if (context.mounted) {
                                FocusScope.of(context).unfocus();
                              }
                            },
                            child: state.loading
                                ? const CircularProgressIndicator.adaptive()
                                : Padding(
                                    padding: const EdgeInsets.symmetric(
                                            horizontal: 34.0)
                                        .w,
                                    child: Text(
                                      'Save',
                                      style: TextStyle(
                                        color: SdmPalette.white,
                                        fontWeight: FontWeight.w500,
                                        fontSize: 14.sp,
                                      ),
                                    ),
                                  ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    Icon(
                      Icons.delete,
                      color: SdmPalette.lightRed,
                      size: 20.h,
                    ),
                    TextButton(
                      onPressed: () async {
                        final shouldDelete = await showDialog<bool>(
                          context: context,
                          builder: (context) => const AdaptiveDialog(
                            title: 'Delete Account',
                            content:
                                'Are you sure you want to delete your account?\nThis action cannot be undone.',
                            confirmText: 'Delete',
                            isDestructive: true,
                          ),
                        );

                        if (shouldDelete == true && context.mounted) {
                          final musicCubit = context.read<MusicPlayerCubit>();
                          final authCubit = context.read<AuthCubit>();
                          final userSessionCubit =
                              context.read<UserSessionCubit>();
                          final userRepository = context.read<UserRepository>();
                          final userId = userSessionCubit.currentUserId;

                          await musicCubit.stop();

                          // Delete user data from Firestore first
                          if (userId != null) {
                            await userRepository.deleteUser(uid: userId);
                          }

                          userSessionCubit.clearSession();
                          await authCubit.deleteAccount();

                          // Navigate to auth screen
                          if (context.mounted) {
                            Navigator.of(context).pushNamedAndRemoveUntil(
                              SdmRouter.phoneRegistration,
                              (route) => false,
                            );
                          }
                        }
                      },
                      child: Text(
                        'Delete Account',
                        style: TextStyle(
                          color: SdmPalette.lightRed,
                          fontWeight: FontWeight.w600,
                          fontSize: 14.sp,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 20.h,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
