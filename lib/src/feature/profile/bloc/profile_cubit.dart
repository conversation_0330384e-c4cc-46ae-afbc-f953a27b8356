import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/core/core.dart' show CubitExt;
import 'package:shridattmandir/src/feature/feature.dart' show UserModel;
import 'package:shridattmandir/src/feature/user/repository/user_repository.dart';
import 'package:shridattmandir/src/shared/shared.dart' show SdmToast;

part 'profile_state.dart';

class ProfileCubit extends Cubit<ProfileState> {
  ProfileCubit() : super(const ProfileState());

  final TextEditingController nameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();

  final _userRepository = UserRepository(
    FirebaseFirestore.instance,
  );

  Future<void> getUserData({
    required String uid,
  }) async {
    safeEmit(
      state.copyWith(
        loading: true,
      ),
    );
    final UserModel? user = await _userRepository.getUser(
      uid: uid,
    );
    if (user != null) {
      nameController.text = user.name ?? '';
      emailController.text = user.email ?? '';
      phoneController.text = user.phoneNumber ?? '';
    }
    safeEmit(
      state.copyWith(
        userModel: user,
        loading: false,
      ),
    );
  }

  Future<void> setUserData({
    required String uid,
  }) async {
    if (!RegExp(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
        .hasMatch(emailController.text)) {
      SdmToast.show('Please enter valid email');
      return;
    }
    if (!RegExp(r"^\+[0-9]{10,15}$").hasMatch(phoneController.text)) {
      SdmToast.show('Please enter valid phone number');
      return;
    }

    safeEmit(
      state.copyWith(
        loading: true,
      ),
    );

    final UserModel user = UserModel(
        uid: uid,
        name: nameController.text,
        email: emailController.text,
        phoneNumber: phoneController.text,
        isAdmin: state.userModel?.isAdmin);
    await _userRepository.updateUser(
      user: user,
    );
    safeEmit(
      state.copyWith(
        userModel: user,
        loading: false,
      ),
    );
  }
}
