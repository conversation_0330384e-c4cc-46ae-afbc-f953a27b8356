part of 'profile_cubit.dart';

final class ProfileState extends Equatable {
  const ProfileState({
    this.userModel,
    this.loading = false,
  });

  final UserModel? userModel;
  final bool loading;

  ProfileState copyWith({
    UserModel? userModel,
    bool? loading,
  }) {
    return ProfileState(
      userModel: userModel ?? this.userModel,
      loading: loading ?? this.loading,
    );
  }

  @override
  List<Object?> get props => [
        userModel,
        loading,
      ];
}
