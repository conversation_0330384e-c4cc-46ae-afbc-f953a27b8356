// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_user_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AuthUserDtoImpl _$$AuthUserDtoImplFromJson(Map<String, dynamic> json) =>
    _$AuthUserDtoImpl(
      uid: json['uid'] as String,
      phoneNumber: json['phoneNumber'] as String,
      email: json['email'] as String?,
      displayName: json['displayName'] as String?,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      lastSignInAt: json['last_sign_in_at'] == null
          ? null
          : DateTime.parse(json['last_sign_in_at'] as String),
      isEmailVerified: json['email_verified'] as bool,
      isPhoneVerified: json['phone_verified'] as bool,
    );

Map<String, dynamic> _$$AuthUserDtoImplToJson(_$AuthUserDtoImpl instance) =>
    <String, dynamic>{
      'uid': instance.uid,
      'phoneNumber': instance.phoneNumber,
      'email': instance.email,
      'displayName': instance.displayName,
      'created_at': instance.createdAt?.toIso8601String(),
      'last_sign_in_at': instance.lastSignInAt?.toIso8601String(),
      'email_verified': instance.isEmailVerified,
      'phone_verified': instance.isPhoneVerified,
    };
