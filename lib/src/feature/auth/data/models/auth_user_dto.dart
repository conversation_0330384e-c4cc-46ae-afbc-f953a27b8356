import 'package:firebase_auth/firebase_auth.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../domain/entities/auth_user.dart';

part 'auth_user_dto.freezed.dart';
part 'auth_user_dto.g.dart';

/// Data Transfer Object for AuthUser
/// Handles conversion between Firebase User and domain AuthUser entity
@freezed
class AuthUserDto with _$AuthUserDto {
  const factory AuthUserDto({
    required String uid,
    required String phoneNumber,
    String? email,
    String? displayName,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at') DateTime? createdAt,
    @J<PERSON><PERSON>ey(name: 'last_sign_in_at') DateTime? lastSignInAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'email_verified') required bool isEmailVerified,
    @J<PERSON><PERSON>ey(name: 'phone_verified') required bool isPhoneVerified,
  }) = _AuthUserDto;
  
  const AuthUserDto._();
  
  /// Create from JSON
  factory AuthUserDto.from<PERSON>son(Map<String, dynamic> json) =>
      _$AuthUserDtoFromJson(json);
  
  /// Create from Firebase User
  factory AuthUserDto.fromFirebaseUser(User firebaseUser) {
    return AuthUserDto(
      uid: firebaseUser.uid,
      phoneNumber: firebaseUser.phoneNumber ?? '',
      email: firebaseUser.email,
      displayName: firebaseUser.displayName,
      createdAt: firebaseUser.metadata.creationTime,
      lastSignInAt: firebaseUser.metadata.lastSignInTime,
      isEmailVerified: firebaseUser.emailVerified,
      isPhoneVerified: firebaseUser.phoneNumber != null,
    );
  }
  
  /// Convert to domain entity
  AuthUser toDomain() {
    return AuthUser(
      uid: uid,
      phoneNumber: phoneNumber,
      email: email,
      displayName: displayName,
      createdAt: createdAt,
      lastSignInAt: lastSignInAt,
      isEmailVerified: isEmailVerified,
      isPhoneVerified: isPhoneVerified,
    );
  }
}
