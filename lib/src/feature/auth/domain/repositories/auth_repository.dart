import '../entities/auth_user.dart';
import '../entities/phone_auth_request.dart';
import '../entities/otp_verification_request.dart';

/// Abstract repository interface for authentication operations
/// This defines the contract that data layer implementations must follow
abstract class AuthRepository {
  
  /// Stream of authentication state changes
  Stream<AuthUser?> get authStateChanges;
  
  /// Get the currently authenticated user
  AuthUser? get currentUser;
  
  /// Send OTP to the provided phone number
  /// Returns a verification ID that will be used for OTP verification
  Future<String> sendOtp(PhoneAuthRequest request);
  
  /// Verify the OTP and complete authentication
  /// Returns the authenticated user on success
  Future<AuthUser> verifyOtp(OtpVerificationRequest request);
  
  /// Sign out the current user
  Future<void> signOut();
  
  /// Delete the current user account
  Future<void> deleteAccount();
  
  /// Refresh the current user's authentication token
  Future<void> refreshToken();
  
  /// Check if a phone number is already registered
  Future<bool> isPhoneNumberRegistered(String phoneNumber);
}
