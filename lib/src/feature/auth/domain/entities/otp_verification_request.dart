import 'package:freezed_annotation/freezed_annotation.dart';

part 'otp_verification_request.freezed.dart';

/// Domain entity for OTP verification request
@freezed
class OtpVerificationRequest with _$OtpVerificationRequest {
  const factory OtpVerificationRequest({
    required String verificationId,
    required String smsCode,
  }) = _OtpVerificationRequest;
  
  const OtpVerificationRequest._();
  
  /// Validate the OTP format
  bool get isValid {
    return verificationId.isNotEmpty && 
           smsCode.isNotEmpty && 
           smsCode.length >= 4; // Minimum OTP length
  }
}
