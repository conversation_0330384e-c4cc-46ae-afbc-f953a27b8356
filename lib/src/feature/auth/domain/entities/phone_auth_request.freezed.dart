// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'phone_auth_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PhoneAuthRequest {
  String get phoneNumber => throw _privateConstructorUsedError;
  String get countryCode => throw _privateConstructorUsedError;
  int? get forceResendingToken => throw _privateConstructorUsedError;
  Duration? get timeout => throw _privateConstructorUsedError;

  /// Create a copy of PhoneAuthRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PhoneAuthRequestCopyWith<PhoneAuthRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PhoneAuthRequestCopyWith<$Res> {
  factory $PhoneAuthRequestCopyWith(
          PhoneAuthRequest value, $Res Function(PhoneAuthRequest) then) =
      _$PhoneAuthRequestCopyWithImpl<$Res, PhoneAuthRequest>;
  @useResult
  $Res call(
      {String phoneNumber,
      String countryCode,
      int? forceResendingToken,
      Duration? timeout});
}

/// @nodoc
class _$PhoneAuthRequestCopyWithImpl<$Res, $Val extends PhoneAuthRequest>
    implements $PhoneAuthRequestCopyWith<$Res> {
  _$PhoneAuthRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PhoneAuthRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = null,
    Object? countryCode = null,
    Object? forceResendingToken = freezed,
    Object? timeout = freezed,
  }) {
    return _then(_value.copyWith(
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      forceResendingToken: freezed == forceResendingToken
          ? _value.forceResendingToken
          : forceResendingToken // ignore: cast_nullable_to_non_nullable
              as int?,
      timeout: freezed == timeout
          ? _value.timeout
          : timeout // ignore: cast_nullable_to_non_nullable
              as Duration?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PhoneAuthRequestImplCopyWith<$Res>
    implements $PhoneAuthRequestCopyWith<$Res> {
  factory _$$PhoneAuthRequestImplCopyWith(_$PhoneAuthRequestImpl value,
          $Res Function(_$PhoneAuthRequestImpl) then) =
      __$$PhoneAuthRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String phoneNumber,
      String countryCode,
      int? forceResendingToken,
      Duration? timeout});
}

/// @nodoc
class __$$PhoneAuthRequestImplCopyWithImpl<$Res>
    extends _$PhoneAuthRequestCopyWithImpl<$Res, _$PhoneAuthRequestImpl>
    implements _$$PhoneAuthRequestImplCopyWith<$Res> {
  __$$PhoneAuthRequestImplCopyWithImpl(_$PhoneAuthRequestImpl _value,
      $Res Function(_$PhoneAuthRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of PhoneAuthRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = null,
    Object? countryCode = null,
    Object? forceResendingToken = freezed,
    Object? timeout = freezed,
  }) {
    return _then(_$PhoneAuthRequestImpl(
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      forceResendingToken: freezed == forceResendingToken
          ? _value.forceResendingToken
          : forceResendingToken // ignore: cast_nullable_to_non_nullable
              as int?,
      timeout: freezed == timeout
          ? _value.timeout
          : timeout // ignore: cast_nullable_to_non_nullable
              as Duration?,
    ));
  }
}

/// @nodoc

class _$PhoneAuthRequestImpl extends _PhoneAuthRequest {
  const _$PhoneAuthRequestImpl(
      {required this.phoneNumber,
      required this.countryCode,
      this.forceResendingToken,
      this.timeout})
      : super._();

  @override
  final String phoneNumber;
  @override
  final String countryCode;
  @override
  final int? forceResendingToken;
  @override
  final Duration? timeout;

  @override
  String toString() {
    return 'PhoneAuthRequest(phoneNumber: $phoneNumber, countryCode: $countryCode, forceResendingToken: $forceResendingToken, timeout: $timeout)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PhoneAuthRequestImpl &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode) &&
            (identical(other.forceResendingToken, forceResendingToken) ||
                other.forceResendingToken == forceResendingToken) &&
            (identical(other.timeout, timeout) || other.timeout == timeout));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, phoneNumber, countryCode, forceResendingToken, timeout);

  /// Create a copy of PhoneAuthRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PhoneAuthRequestImplCopyWith<_$PhoneAuthRequestImpl> get copyWith =>
      __$$PhoneAuthRequestImplCopyWithImpl<_$PhoneAuthRequestImpl>(
          this, _$identity);
}

abstract class _PhoneAuthRequest extends PhoneAuthRequest {
  const factory _PhoneAuthRequest(
      {required final String phoneNumber,
      required final String countryCode,
      final int? forceResendingToken,
      final Duration? timeout}) = _$PhoneAuthRequestImpl;
  const _PhoneAuthRequest._() : super._();

  @override
  String get phoneNumber;
  @override
  String get countryCode;
  @override
  int? get forceResendingToken;
  @override
  Duration? get timeout;

  /// Create a copy of PhoneAuthRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PhoneAuthRequestImplCopyWith<_$PhoneAuthRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
