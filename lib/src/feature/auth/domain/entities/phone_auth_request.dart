import 'package:freezed_annotation/freezed_annotation.dart';

part 'phone_auth_request.freezed.dart';

/// Domain entity for phone authentication request
@freezed
class PhoneAuthRequest with _$PhoneAuthRequest {
  const factory PhoneAuthRequest({
    required String phoneNumber,
    required String countryCode,
    int? forceResendingToken,
    Duration? timeout,
  }) = _PhoneAuthRequest;
  
  const PhoneAuthRequest._();
  
  /// Get the complete phone number with country code
  String get completePhoneNumber => '$countryCode$phoneNumber';
  
  /// Validate the phone number format
  bool get isValid {
    // Basic validation - can be enhanced
    return phoneNumber.isNotEmpty && 
           phoneNumber.length >= 10 && 
           countryCode.isNotEmpty;
  }
}
