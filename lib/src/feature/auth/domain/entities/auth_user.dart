import 'package:freezed_annotation/freezed_annotation.dart';

part 'auth_user.freezed.dart';

/// Domain entity representing an authenticated user
/// This is independent of any external dependencies (Firebase, etc.)
@freezed
class AuthUser with _$AuthUser {
  const factory AuthUser({
    required String uid,
    required String phoneNumber,
    String? email,
    String? displayName,
    DateTime? createdAt,
    DateTime? lastSignInAt,
    required bool isEmailVerified,
    required bool isPhoneVerified,
  }) = _AuthUser;
  
  const AuthUser._();
  
  /// Check if the user has completed basic profile information
  bool get hasBasicProfile => displayName != null && displayName!.isNotEmpty;
  
  /// Check if the user is fully verified
  bool get isFullyVerified => isEmailVerified && isPhoneVerified;
}
