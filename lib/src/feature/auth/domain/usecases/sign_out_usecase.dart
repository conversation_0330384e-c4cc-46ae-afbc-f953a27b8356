import '../repositories/auth_repository.dart';

/// Use case for signing out the current user
class SignOutUseCase {
  const SignOutUseCase(this._authRepository);
  
  final AuthRepository _authRepository;
  
  /// Execute the use case
  Future<void> call() async {
    // Get current user before signing out (for logging/analytics)
    final currentUser = _authRepository.currentUser;
    
    if (currentUser == null) {
      // User is already signed out
      return;
    }
    
    // Perform sign out
    await _authRepository.signOut();
    
    // Additional cleanup logic can be added here
    // For example: clearing local storage, stopping services, etc.
  }
}
