import 'package:firebase_auth/firebase_auth.dart';
import 'package:get_it/get_it.dart';

import '../data/repositories/firebase_auth_repository.dart';
import '../domain/repositories/auth_repository.dart';
import '../domain/usecases/send_otp_usecase.dart';
import '../domain/usecases/verify_otp_usecase.dart';
import '../domain/usecases/sign_out_usecase.dart';
import '../presentation/bloc/auth_bloc.dart';

/// Dependency injection setup for the authentication module
class AuthDI {
  
  /// Register all authentication dependencies
  static void registerDependencies(GetIt getIt) {
    
    // Register repository implementation
    getIt.registerLazySingleton<AuthRepository>(
      () => FirebaseAuthRepository(getIt<FirebaseAuth>()),
    );
    
    // Register use cases
    getIt.registerLazySingleton<SendOtpUseCase>(
      () => SendOtpUseCase(getIt<AuthRepository>()),
    );
    
    getIt.registerLazySingleton<VerifyOtpUseCase>(
      () => VerifyOtpUseCase(getIt<AuthRepository>()),
    );
    
    getIt.registerLazySingleton<SignOutUseCase>(
      () => SignOutUseCase(getIt<AuthRepository>()),
    );
    
    // Register BLoC factory
    getIt.registerFactory<AuthBloc>(
      () => AuthBloc(
        authRepository: getIt<AuthRepository>(),
        sendOtpUseCase: getIt<SendOtpUseCase>(),
        verifyOtpUseCase: getIt<VerifyOtpUseCase>(),
        signOutUseCase: getIt<SignOutUseCase>(),
      ),
    );
  }
  
  /// Unregister all authentication dependencies
  /// Useful for testing
  static void unregisterDependencies(GetIt getIt) {
    getIt.unregister<AuthBloc>();
    getIt.unregister<SignOutUseCase>();
    getIt.unregister<VerifyOtpUseCase>();
    getIt.unregister<SendOtpUseCase>();
    getIt.unregister<AuthRepository>();
  }
}
