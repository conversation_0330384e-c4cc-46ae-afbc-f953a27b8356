import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/core/core.dart' show CubitExt;
import 'package:shridattmandir/src/feature/feature.dart'
    show AuthRepository, AuthStateEnum;
import 'package:shridattmandir/src/shared/shared.dart' show SdmToast;

part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  AuthCubit() : super(const AuthState());

  final TextEditingController phoneNumberController = TextEditingController();
  final TextEditingController smsCodeController = TextEditingController();
  final TextEditingController countryCodeController =
      TextEditingController(text: '+91');

  final AuthRepository _authRepository = AuthRepository(
    FirebaseAuth.instance,
  );

  void listenToUserAuthChanges() {
    _authRepository.userChanges.listen(
      (User? user) {
        safeEmit(
          state.copyWith(
            user: () => user,
          ),
        );
      },
    );
  }

  Future<void> verifyPhoneNumber(
      {int? forceResendingToken, bool resendOtp = false}) async {
    safeEmit(
      state.copyWith(
        loading: true,
      ),
    );

    try {
      await _authRepository.verifyPhoneNumber(
        codeAutoRetrievalTimeout: (String verificationId) {},
        phoneNumber: countryCodeController.text.trim() +
            phoneNumberController.text.trim(),
        forceResendingToken: forceResendingToken,
        timeout: const Duration(
          seconds: 60,
        ),
        verificationCompleted: (PhoneAuthCredential phoneAuthCredential) async {
          await signInWithCredential(
            phoneAuthCredential: phoneAuthCredential,
          );
          safeEmit(
            state.copyWith(
              loading: false,
            ),
          );
        },
        verificationFailed: (FirebaseAuthException authException) {
          SdmToast.show(
            authException.message ?? 'Something went wrong',
          );
          safeEmit(
            state.copyWith(
              loading: false,
            ),
          );
        },
        codeSent: (String verificationId, int? forceResendingToken) {
          safeEmit(
            state.copyWith(
              loading: false,
              verificationId: () => verificationId,
              authStateEnum: AuthStateEnum.verification,
            ),
          );

          SdmToast.show(
            'OTP sent successfully',
          );

          // Navigation will be handled by the UI layer

          if (forceResendingToken != null) {
            safeEmit(
              state.copyWith(
                forceResendingToken: () => forceResendingToken,
              ),
            );
          }
        },
      );
    } catch (e) {
      SdmToast.show(
        e.toString(),
      );
      safeEmit(
        state.copyWith(
          loading: false,
        ),
      );
    }
  }

  Future<void> signInWithCredential({
    PhoneAuthCredential? phoneAuthCredential,
  }) async {
    safeEmit(
      state.copyWith(
        loading: true,
      ),
    );

    try {
      final userCredential = await _authRepository.signInWithCredential(
        phoneAuthCredential ??
            PhoneAuthProvider.credential(
              verificationId: state.verificationId ?? '',
              smsCode: smsCodeController.text.trim(),
            ),
      );

      safeEmit(
        state.copyWith(
          user: () => userCredential.user,
          verificationId: () => null,
          forceResendingToken: () => null,
          authStateEnum: AuthStateEnum.authenticated,
        ),
      );

      //clear controllers
      phoneNumberController.clear();
      smsCodeController.clear();
    } on FirebaseAuthException catch (e) {
      SdmToast.show(
        e.message ?? 'Something went wrong',
      );
    } catch (e) {
      SdmToast.show(
        e.toString(),
      );
    }

    safeEmit(
      state.copyWith(
        loading: false,
      ),
    );
  }

  Future<void> signOut() async {
    safeEmit(
      state.copyWith(
        loading: true,
      ),
    );
    try {
      await _authRepository.signOut();
      safeEmit(
        state.copyWith(
          authStateEnum: AuthStateEnum.registration,
        ),
      );
    } catch (e) {
      SdmToast.show(
        e.toString(),
      );
    }
    safeEmit(
      state.copyWith(
        loading: false,
      ),
    );
  }

  Future<void> deleteAccount() async {
    safeEmit(
      state.copyWith(
        loading: true,
      ),
    );
    try {
      await _authRepository.deleteUser();
      safeEmit(
        state.copyWith(
          authStateEnum: AuthStateEnum.registration,
        ),
      );
    } catch (e) {
      SdmToast.show(
        e.toString(),
      );
    }
    safeEmit(
      state.copyWith(
        loading: false,
      ),
    );
  }

  @override
  Future<void> close() {
    phoneNumberController.dispose();
    smsCodeController.dispose();

    return super.close();
  }
}
