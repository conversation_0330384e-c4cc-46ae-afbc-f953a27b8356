part of 'auth_cubit.dart';

final class AuthState extends Equatable {
  const AuthState({
    this.loading = false,
    this.verificationId,
    this.forceResendingToken,
    this.user,
    this.authStateEnum = AuthStateEnum.registration,
    this.showResendOtp = false,
  });

  final bool loading;
  final String? verificationId;
  final int? forceResendingToken;
  final User? user;
  final AuthStateEnum authStateEnum;
  final bool showResendOtp;

  AuthState copyWith({
    bool? loading,
    String? Function()? verificationId,
    int? Function()? forceResendingToken,
    User? Function()? user,
    AuthStateEnum? authStateEnum,
    bool? showResendOtp,
  }) {
    return AuthState(
      loading: loading ?? this.loading,
      verificationId:
          verificationId != null ? verificationId() : this.verificationId,
      forceResendingToken: forceResendingToken != null
          ? forceResendingToken()
          : this.forceResendingToken,
      user: user != null ? user() : this.user,
      authStateEnum: authStateEnum ?? this.authStateEnum,
      showResendOtp: showResendOtp ?? this.showResendOtp,
    );
  }

  @override
  List<Object?> get props => [
        loading,
        verificationId,
        forceResendingToken,
        user,
        authStateEnum,
        showResendOtp,
      ];
}
