import 'package:firebase_auth/firebase_auth.dart';

class AuthRepository {
  AuthRepository(
    FirebaseAuth firebaseAuth,
  ) : _firebaseAuth = firebaseAuth;

  final FirebaseAuth _firebaseAuth;

  Stream<User?> get userChanges => _firebaseAuth.userChanges();

  Future<void> verifyPhoneNumber({
    String? phoneNumber,
    PhoneMultiFactorInfo? multiFactorInfo,
    required void Function(PhoneAuthCredential) verificationCompleted,
    required void Function(FirebaseAuthException) verificationFailed,
    required void Function(String, int?) codeSent,
    required void Function(String) codeAutoRetrievalTimeout,
    String? autoRetrievedSmsCodeForTesting,
    Duration timeout = const Duration(seconds: 30),
    int? forceResendingToken,
    MultiFactorSession? multiFactorSession,
  }) async {
    await _firebaseAuth.verifyPhoneNumber(
      phoneNumber: phoneNumber,
      forceResendingToken: forceResendingToken,
      timeout: timeout,
      verificationCompleted: verificationCompleted,
      verificationFailed: verificationFailed,
      codeSent: codeSent,
      codeAutoRetrievalTimeout: codeAutoRetrievalTimeout,
      multiFactorInfo: multiFactorInfo,
      multiFactorSession: multiFactorSession,
    );
  }

  Future<UserCredential> signInWithCredential(
    PhoneAuthCredential phoneAuthCredential,
  ) async {
    return await _firebaseAuth.signInWithCredential(phoneAuthCredential);
  }

  Future<void> signOut() async {
    await _firebaseAuth.signOut();
  }

  Future<void> deleteUser() async {
    await _firebaseAuth.currentUser?.delete();
  }
}
