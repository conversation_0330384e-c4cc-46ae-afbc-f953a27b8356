import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart'
    show Assets, SdmPalette, SdmRouter;
import 'package:shridattmandir/src/feature/feature.dart'
    show AnalyticsCubit, SendOtpClickEvent, AuthCubit, AuthState, AuthStateEnum;
import 'package:shridattmandir/src/shared/shared.dart'
    show SdmPrimaryCta, SdmTextField;

class PhoneRegistrationScreen extends StatelessWidget {
  const PhoneRegistrationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthCubit, AuthState>(
      listenWhen: (previous, current) =>
          previous.authStateEnum != current.authStateEnum,
      listener: (context, state) {
        if (state.authStateEnum == AuthStateEnum.verification) {
          Navigator.pushNamed(context, SdmRouter.phoneOtp);
        }
      },
      child: BlocBuilder<AuthCubit, AuthState>(
        builder: (context, state) {
          return Scaffold(
            appBar: AppBar(
              foregroundColor: SdmPalette.black,
            ),
            body: ListView(
              physics: const ClampingScrollPhysics(),
              children: [
                SizedBox(
                  height: 72.h,
                ),
                Assets.sdmImages.registratioIllustration.image(
                  height: 196.h,
                ),
                SizedBox(
                  height: 12.h,
                ),
                Text(
                  'Welcome',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 17.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 12.h,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 26.0).w,
                  child: Text(
                    'Continue with your phone number.\nWe will send you a 6 digit code to verify.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w400,
                      color: SdmPalette.textColorGrey,
                    ),
                  ),
                ),
                SizedBox(
                  height: 30.h,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 40.0).w,
                  child: Row(
                    children: [
                      SizedBox(
                        width: 60.w,
                        child: SdmTextField(
                          controller:
                              context.read<AuthCubit>().countryCodeController,
                          textAlign: TextAlign.center,
                          hintText: '+91',
                          maxLines: 1,
                          fontSize: 14.sp,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 12.w,
                            vertical: 7.h,
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 8.w,
                      ),
                      Flexible(
                        child: SdmTextField(
                          fontSize: 14.sp,
                          controller:
                              context.read<AuthCubit>().phoneNumberController,
                          hintText: '9876543210',
                          keyboardType: TextInputType.phone,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 12.w,
                            vertical: 7.h,
                          ),
                          onChanged: (value) {
                            //on 10 digit number pull down the keyboard
                            if (value.length == 10) {
                              FocusScope.of(context).unfocus();
                            }
                          },
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                              RegExp(r'[0-9+]'),
                            ),
                            LengthLimitingTextInputFormatter(15),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 30.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ConstrainedBox(
                      constraints: BoxConstraints(
                        minWidth: 130.w,
                        maxWidth: 130.w,
                      ),
                      child: SdmPrimaryCta(
                        child: state.loading
                            ? const CircularProgressIndicator.adaptive()
                            : Text(
                                'Send',
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  color: SdmPalette.white,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                        onPressed: () async {
                          context
                              .read<AnalyticsCubit>()
                              .onTrackAnalyticsEvent(SendOtpClickEvent());
                          await context.read<AuthCubit>().verifyPhoneNumber();
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
