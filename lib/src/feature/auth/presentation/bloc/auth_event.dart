import 'package:freezed_annotation/freezed_annotation.dart';

import '../../domain/entities/phone_auth_request.dart';
import '../../domain/entities/otp_verification_request.dart';

part 'auth_event.freezed.dart';

/// Events for the authentication BLoC
@freezed
class AuthEvent with _$AuthEvent {
  
  /// Send OTP to phone number
  const factory AuthEvent.sendOtp({
    required String phoneNumber,
    required String countryCode,
    int? forceResendingToken,
  }) = SendOtpEvent;
  
  /// Verify OTP code
  const factory AuthEvent.verifyOtp({
    required String verificationId,
    required String smsCode,
  }) = VerifyOtpEvent;
  
  /// Sign out current user
  const factory AuthEvent.signOut() = SignOutEvent;
  
  /// Delete current user account
  const factory AuthEvent.deleteAccount() = DeleteAccountEvent;
  
  /// Listen to authentication state changes
  const factory AuthEvent.authStateChanged() = AuthStateChangedEvent;
  
  /// Reset authentication state
  const factory AuthEvent.reset() = ResetEvent;
}
