import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';

import '../../domain/entities/otp_verification_request.dart';
import '../../domain/entities/phone_auth_request.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/usecases/send_otp_usecase.dart';
import '../../domain/usecases/sign_out_usecase.dart';
import '../../domain/usecases/verify_otp_usecase.dart';
import 'auth_event.dart';
import 'auth_state.dart';

/// BLoC for handling authentication operations using clean architecture
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  AuthBloc({
    required AuthRepository authRepository,
    required SendOtpUseCase sendOtpUseCase,
    required VerifyOtpUseCase verifyOtpUseCase,
    required SignOutUseCase signOutUseCase,
  })  : _authRepository = authRepository,
        _sendOtpUseCase = sendOtpUseCase,
        _verifyOtpUseCase = verifyOtpUseCase,
        _signOutUseCase = signOutUseCase,
        super(const AuthState()) {
    // Register event handlers
    on<SendOtpEvent>(_onSendOtp);
    on<VerifyOtpEvent>(_onVerifyOtp);
    on<SignOutEvent>(_onSignOut);
    on<DeleteAccountEvent>(_onDeleteAccount);
    on<AuthStateChangedEvent>(_onAuthStateChanged);
    on<ResetEvent>(_onReset);

    // Listen to authentication state changes
    _authStateSubscription = _authRepository.authStateChanges.listen(
      (user) {
        if (user != null) {
          add(const AuthEvent.authStateChanged());
        } else {
          add(const AuthEvent.authStateChanged());
        }
      },
    );
  }

  final AuthRepository _authRepository;
  final SendOtpUseCase _sendOtpUseCase;
  final VerifyOtpUseCase _verifyOtpUseCase;
  final SignOutUseCase _signOutUseCase;

  StreamSubscription? _authStateSubscription;

  /// Handle send OTP event
  Future<void> _onSendOtp(SendOtpEvent event, Emitter<AuthState> emit) async {
    try {
      emit(state.copyWith(status: AuthStatus.loading, errorMessage: null));

      final request = PhoneAuthRequest(
        phoneNumber: event.phoneNumber,
        countryCode: event.countryCode,
        forceResendingToken: event.forceResendingToken,
        timeout: const Duration(seconds: 60),
      );

      final verificationId = await _sendOtpUseCase(request);

      emit(state.copyWith(
        status: AuthStatus.otpSent,
        verificationId: verificationId,
        canResendOtp: false,
      ));

      // Enable resend after 30 seconds
      Timer(const Duration(seconds: 30), () {
        if (!isClosed) {
          emit(state.copyWith(canResendOtp: true));
        }
      });
    } catch (e) {
      emit(state.copyWith(
        status: AuthStatus.error,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Handle verify OTP event
  Future<void> _onVerifyOtp(
      VerifyOtpEvent event, Emitter<AuthState> emit) async {
    try {
      emit(state.copyWith(status: AuthStatus.loading, errorMessage: null));

      final request = OtpVerificationRequest(
        verificationId: event.verificationId,
        smsCode: event.smsCode,
      );

      final user = await _verifyOtpUseCase(request);

      emit(state.copyWith(
        status: AuthStatus.authenticated,
        user: user,
        verificationId: null,
        forceResendingToken: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: AuthStatus.error,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Handle sign out event
  Future<void> _onSignOut(SignOutEvent event, Emitter<AuthState> emit) async {
    try {
      emit(state.copyWith(status: AuthStatus.loading, errorMessage: null));

      await _signOutUseCase();

      emit(state.copyWith(
        status: AuthStatus.unauthenticated,
        user: null,
        verificationId: null,
        forceResendingToken: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: AuthStatus.error,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Handle delete account event
  Future<void> _onDeleteAccount(
      DeleteAccountEvent event, Emitter<AuthState> emit) async {
    try {
      emit(state.copyWith(status: AuthStatus.loading, errorMessage: null));

      await _authRepository.deleteAccount();

      emit(state.copyWith(
        status: AuthStatus.unauthenticated,
        user: null,
        verificationId: null,
        forceResendingToken: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: AuthStatus.error,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Handle auth state changed event
  void _onAuthStateChanged(
      AuthStateChangedEvent event, Emitter<AuthState> emit) {
    final user = _authRepository.currentUser;
    if (user != null) {
      emit(state.copyWith(
        status: AuthStatus.authenticated,
        user: user,
        errorMessage: null,
      ));
    } else {
      emit(state.copyWith(
        status: AuthStatus.unauthenticated,
        user: null,
        verificationId: null,
        forceResendingToken: null,
        errorMessage: null,
      ));
    }
  }

  /// Handle reset event
  void _onReset(ResetEvent event, Emitter<AuthState> emit) {
    emit(const AuthState());
  }

  @override
  Future<void> close() {
    _authStateSubscription?.cancel();
    return super.close();
  }
}
