// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AuthEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String phoneNumber, String countryCode, int? forceResendingToken)
        sendOtp,
    required TResult Function(String verificationId, String smsCode) verifyOtp,
    required TResult Function() signOut,
    required TResult Function() deleteAccount,
    required TResult Function() authStateChanged,
    required TResult Function() reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String phoneNumber, String countryCode, int? forceResendingToken)?
        sendOtp,
    TResult? Function(String verificationId, String smsCode)? verifyOtp,
    TResult? Function()? signOut,
    TResult? Function()? deleteAccount,
    TResult? Function()? authStateChanged,
    TResult? Function()? reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String phoneNumber, String countryCode, int? forceResendingToken)?
        sendOtp,
    TResult Function(String verificationId, String smsCode)? verifyOtp,
    TResult Function()? signOut,
    TResult Function()? deleteAccount,
    TResult Function()? authStateChanged,
    TResult Function()? reset,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(SendOtpEvent value) sendOtp,
    required TResult Function(VerifyOtpEvent value) verifyOtp,
    required TResult Function(SignOutEvent value) signOut,
    required TResult Function(DeleteAccountEvent value) deleteAccount,
    required TResult Function(AuthStateChangedEvent value) authStateChanged,
    required TResult Function(ResetEvent value) reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(SendOtpEvent value)? sendOtp,
    TResult? Function(VerifyOtpEvent value)? verifyOtp,
    TResult? Function(SignOutEvent value)? signOut,
    TResult? Function(DeleteAccountEvent value)? deleteAccount,
    TResult? Function(AuthStateChangedEvent value)? authStateChanged,
    TResult? Function(ResetEvent value)? reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(SendOtpEvent value)? sendOtp,
    TResult Function(VerifyOtpEvent value)? verifyOtp,
    TResult Function(SignOutEvent value)? signOut,
    TResult Function(DeleteAccountEvent value)? deleteAccount,
    TResult Function(AuthStateChangedEvent value)? authStateChanged,
    TResult Function(ResetEvent value)? reset,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthEventCopyWith<$Res> {
  factory $AuthEventCopyWith(AuthEvent value, $Res Function(AuthEvent) then) =
      _$AuthEventCopyWithImpl<$Res, AuthEvent>;
}

/// @nodoc
class _$AuthEventCopyWithImpl<$Res, $Val extends AuthEvent>
    implements $AuthEventCopyWith<$Res> {
  _$AuthEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$SendOtpEventImplCopyWith<$Res> {
  factory _$$SendOtpEventImplCopyWith(
          _$SendOtpEventImpl value, $Res Function(_$SendOtpEventImpl) then) =
      __$$SendOtpEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String phoneNumber, String countryCode, int? forceResendingToken});
}

/// @nodoc
class __$$SendOtpEventImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$SendOtpEventImpl>
    implements _$$SendOtpEventImplCopyWith<$Res> {
  __$$SendOtpEventImplCopyWithImpl(
      _$SendOtpEventImpl _value, $Res Function(_$SendOtpEventImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = null,
    Object? countryCode = null,
    Object? forceResendingToken = freezed,
  }) {
    return _then(_$SendOtpEventImpl(
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      forceResendingToken: freezed == forceResendingToken
          ? _value.forceResendingToken
          : forceResendingToken // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

class _$SendOtpEventImpl implements SendOtpEvent {
  const _$SendOtpEventImpl(
      {required this.phoneNumber,
      required this.countryCode,
      this.forceResendingToken});

  @override
  final String phoneNumber;
  @override
  final String countryCode;
  @override
  final int? forceResendingToken;

  @override
  String toString() {
    return 'AuthEvent.sendOtp(phoneNumber: $phoneNumber, countryCode: $countryCode, forceResendingToken: $forceResendingToken)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SendOtpEventImpl &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode) &&
            (identical(other.forceResendingToken, forceResendingToken) ||
                other.forceResendingToken == forceResendingToken));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, phoneNumber, countryCode, forceResendingToken);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SendOtpEventImplCopyWith<_$SendOtpEventImpl> get copyWith =>
      __$$SendOtpEventImplCopyWithImpl<_$SendOtpEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String phoneNumber, String countryCode, int? forceResendingToken)
        sendOtp,
    required TResult Function(String verificationId, String smsCode) verifyOtp,
    required TResult Function() signOut,
    required TResult Function() deleteAccount,
    required TResult Function() authStateChanged,
    required TResult Function() reset,
  }) {
    return sendOtp(phoneNumber, countryCode, forceResendingToken);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String phoneNumber, String countryCode, int? forceResendingToken)?
        sendOtp,
    TResult? Function(String verificationId, String smsCode)? verifyOtp,
    TResult? Function()? signOut,
    TResult? Function()? deleteAccount,
    TResult? Function()? authStateChanged,
    TResult? Function()? reset,
  }) {
    return sendOtp?.call(phoneNumber, countryCode, forceResendingToken);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String phoneNumber, String countryCode, int? forceResendingToken)?
        sendOtp,
    TResult Function(String verificationId, String smsCode)? verifyOtp,
    TResult Function()? signOut,
    TResult Function()? deleteAccount,
    TResult Function()? authStateChanged,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (sendOtp != null) {
      return sendOtp(phoneNumber, countryCode, forceResendingToken);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(SendOtpEvent value) sendOtp,
    required TResult Function(VerifyOtpEvent value) verifyOtp,
    required TResult Function(SignOutEvent value) signOut,
    required TResult Function(DeleteAccountEvent value) deleteAccount,
    required TResult Function(AuthStateChangedEvent value) authStateChanged,
    required TResult Function(ResetEvent value) reset,
  }) {
    return sendOtp(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(SendOtpEvent value)? sendOtp,
    TResult? Function(VerifyOtpEvent value)? verifyOtp,
    TResult? Function(SignOutEvent value)? signOut,
    TResult? Function(DeleteAccountEvent value)? deleteAccount,
    TResult? Function(AuthStateChangedEvent value)? authStateChanged,
    TResult? Function(ResetEvent value)? reset,
  }) {
    return sendOtp?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(SendOtpEvent value)? sendOtp,
    TResult Function(VerifyOtpEvent value)? verifyOtp,
    TResult Function(SignOutEvent value)? signOut,
    TResult Function(DeleteAccountEvent value)? deleteAccount,
    TResult Function(AuthStateChangedEvent value)? authStateChanged,
    TResult Function(ResetEvent value)? reset,
    required TResult orElse(),
  }) {
    if (sendOtp != null) {
      return sendOtp(this);
    }
    return orElse();
  }
}

abstract class SendOtpEvent implements AuthEvent {
  const factory SendOtpEvent(
      {required final String phoneNumber,
      required final String countryCode,
      final int? forceResendingToken}) = _$SendOtpEventImpl;

  String get phoneNumber;
  String get countryCode;
  int? get forceResendingToken;

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SendOtpEventImplCopyWith<_$SendOtpEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$VerifyOtpEventImplCopyWith<$Res> {
  factory _$$VerifyOtpEventImplCopyWith(_$VerifyOtpEventImpl value,
          $Res Function(_$VerifyOtpEventImpl) then) =
      __$$VerifyOtpEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String verificationId, String smsCode});
}

/// @nodoc
class __$$VerifyOtpEventImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$VerifyOtpEventImpl>
    implements _$$VerifyOtpEventImplCopyWith<$Res> {
  __$$VerifyOtpEventImplCopyWithImpl(
      _$VerifyOtpEventImpl _value, $Res Function(_$VerifyOtpEventImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? verificationId = null,
    Object? smsCode = null,
  }) {
    return _then(_$VerifyOtpEventImpl(
      verificationId: null == verificationId
          ? _value.verificationId
          : verificationId // ignore: cast_nullable_to_non_nullable
              as String,
      smsCode: null == smsCode
          ? _value.smsCode
          : smsCode // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$VerifyOtpEventImpl implements VerifyOtpEvent {
  const _$VerifyOtpEventImpl(
      {required this.verificationId, required this.smsCode});

  @override
  final String verificationId;
  @override
  final String smsCode;

  @override
  String toString() {
    return 'AuthEvent.verifyOtp(verificationId: $verificationId, smsCode: $smsCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerifyOtpEventImpl &&
            (identical(other.verificationId, verificationId) ||
                other.verificationId == verificationId) &&
            (identical(other.smsCode, smsCode) || other.smsCode == smsCode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, verificationId, smsCode);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerifyOtpEventImplCopyWith<_$VerifyOtpEventImpl> get copyWith =>
      __$$VerifyOtpEventImplCopyWithImpl<_$VerifyOtpEventImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String phoneNumber, String countryCode, int? forceResendingToken)
        sendOtp,
    required TResult Function(String verificationId, String smsCode) verifyOtp,
    required TResult Function() signOut,
    required TResult Function() deleteAccount,
    required TResult Function() authStateChanged,
    required TResult Function() reset,
  }) {
    return verifyOtp(verificationId, smsCode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String phoneNumber, String countryCode, int? forceResendingToken)?
        sendOtp,
    TResult? Function(String verificationId, String smsCode)? verifyOtp,
    TResult? Function()? signOut,
    TResult? Function()? deleteAccount,
    TResult? Function()? authStateChanged,
    TResult? Function()? reset,
  }) {
    return verifyOtp?.call(verificationId, smsCode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String phoneNumber, String countryCode, int? forceResendingToken)?
        sendOtp,
    TResult Function(String verificationId, String smsCode)? verifyOtp,
    TResult Function()? signOut,
    TResult Function()? deleteAccount,
    TResult Function()? authStateChanged,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (verifyOtp != null) {
      return verifyOtp(verificationId, smsCode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(SendOtpEvent value) sendOtp,
    required TResult Function(VerifyOtpEvent value) verifyOtp,
    required TResult Function(SignOutEvent value) signOut,
    required TResult Function(DeleteAccountEvent value) deleteAccount,
    required TResult Function(AuthStateChangedEvent value) authStateChanged,
    required TResult Function(ResetEvent value) reset,
  }) {
    return verifyOtp(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(SendOtpEvent value)? sendOtp,
    TResult? Function(VerifyOtpEvent value)? verifyOtp,
    TResult? Function(SignOutEvent value)? signOut,
    TResult? Function(DeleteAccountEvent value)? deleteAccount,
    TResult? Function(AuthStateChangedEvent value)? authStateChanged,
    TResult? Function(ResetEvent value)? reset,
  }) {
    return verifyOtp?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(SendOtpEvent value)? sendOtp,
    TResult Function(VerifyOtpEvent value)? verifyOtp,
    TResult Function(SignOutEvent value)? signOut,
    TResult Function(DeleteAccountEvent value)? deleteAccount,
    TResult Function(AuthStateChangedEvent value)? authStateChanged,
    TResult Function(ResetEvent value)? reset,
    required TResult orElse(),
  }) {
    if (verifyOtp != null) {
      return verifyOtp(this);
    }
    return orElse();
  }
}

abstract class VerifyOtpEvent implements AuthEvent {
  const factory VerifyOtpEvent(
      {required final String verificationId,
      required final String smsCode}) = _$VerifyOtpEventImpl;

  String get verificationId;
  String get smsCode;

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerifyOtpEventImplCopyWith<_$VerifyOtpEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SignOutEventImplCopyWith<$Res> {
  factory _$$SignOutEventImplCopyWith(
          _$SignOutEventImpl value, $Res Function(_$SignOutEventImpl) then) =
      __$$SignOutEventImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SignOutEventImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$SignOutEventImpl>
    implements _$$SignOutEventImplCopyWith<$Res> {
  __$$SignOutEventImplCopyWithImpl(
      _$SignOutEventImpl _value, $Res Function(_$SignOutEventImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SignOutEventImpl implements SignOutEvent {
  const _$SignOutEventImpl();

  @override
  String toString() {
    return 'AuthEvent.signOut()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SignOutEventImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String phoneNumber, String countryCode, int? forceResendingToken)
        sendOtp,
    required TResult Function(String verificationId, String smsCode) verifyOtp,
    required TResult Function() signOut,
    required TResult Function() deleteAccount,
    required TResult Function() authStateChanged,
    required TResult Function() reset,
  }) {
    return signOut();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String phoneNumber, String countryCode, int? forceResendingToken)?
        sendOtp,
    TResult? Function(String verificationId, String smsCode)? verifyOtp,
    TResult? Function()? signOut,
    TResult? Function()? deleteAccount,
    TResult? Function()? authStateChanged,
    TResult? Function()? reset,
  }) {
    return signOut?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String phoneNumber, String countryCode, int? forceResendingToken)?
        sendOtp,
    TResult Function(String verificationId, String smsCode)? verifyOtp,
    TResult Function()? signOut,
    TResult Function()? deleteAccount,
    TResult Function()? authStateChanged,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (signOut != null) {
      return signOut();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(SendOtpEvent value) sendOtp,
    required TResult Function(VerifyOtpEvent value) verifyOtp,
    required TResult Function(SignOutEvent value) signOut,
    required TResult Function(DeleteAccountEvent value) deleteAccount,
    required TResult Function(AuthStateChangedEvent value) authStateChanged,
    required TResult Function(ResetEvent value) reset,
  }) {
    return signOut(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(SendOtpEvent value)? sendOtp,
    TResult? Function(VerifyOtpEvent value)? verifyOtp,
    TResult? Function(SignOutEvent value)? signOut,
    TResult? Function(DeleteAccountEvent value)? deleteAccount,
    TResult? Function(AuthStateChangedEvent value)? authStateChanged,
    TResult? Function(ResetEvent value)? reset,
  }) {
    return signOut?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(SendOtpEvent value)? sendOtp,
    TResult Function(VerifyOtpEvent value)? verifyOtp,
    TResult Function(SignOutEvent value)? signOut,
    TResult Function(DeleteAccountEvent value)? deleteAccount,
    TResult Function(AuthStateChangedEvent value)? authStateChanged,
    TResult Function(ResetEvent value)? reset,
    required TResult orElse(),
  }) {
    if (signOut != null) {
      return signOut(this);
    }
    return orElse();
  }
}

abstract class SignOutEvent implements AuthEvent {
  const factory SignOutEvent() = _$SignOutEventImpl;
}

/// @nodoc
abstract class _$$DeleteAccountEventImplCopyWith<$Res> {
  factory _$$DeleteAccountEventImplCopyWith(_$DeleteAccountEventImpl value,
          $Res Function(_$DeleteAccountEventImpl) then) =
      __$$DeleteAccountEventImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DeleteAccountEventImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$DeleteAccountEventImpl>
    implements _$$DeleteAccountEventImplCopyWith<$Res> {
  __$$DeleteAccountEventImplCopyWithImpl(_$DeleteAccountEventImpl _value,
      $Res Function(_$DeleteAccountEventImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DeleteAccountEventImpl implements DeleteAccountEvent {
  const _$DeleteAccountEventImpl();

  @override
  String toString() {
    return 'AuthEvent.deleteAccount()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DeleteAccountEventImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String phoneNumber, String countryCode, int? forceResendingToken)
        sendOtp,
    required TResult Function(String verificationId, String smsCode) verifyOtp,
    required TResult Function() signOut,
    required TResult Function() deleteAccount,
    required TResult Function() authStateChanged,
    required TResult Function() reset,
  }) {
    return deleteAccount();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String phoneNumber, String countryCode, int? forceResendingToken)?
        sendOtp,
    TResult? Function(String verificationId, String smsCode)? verifyOtp,
    TResult? Function()? signOut,
    TResult? Function()? deleteAccount,
    TResult? Function()? authStateChanged,
    TResult? Function()? reset,
  }) {
    return deleteAccount?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String phoneNumber, String countryCode, int? forceResendingToken)?
        sendOtp,
    TResult Function(String verificationId, String smsCode)? verifyOtp,
    TResult Function()? signOut,
    TResult Function()? deleteAccount,
    TResult Function()? authStateChanged,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (deleteAccount != null) {
      return deleteAccount();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(SendOtpEvent value) sendOtp,
    required TResult Function(VerifyOtpEvent value) verifyOtp,
    required TResult Function(SignOutEvent value) signOut,
    required TResult Function(DeleteAccountEvent value) deleteAccount,
    required TResult Function(AuthStateChangedEvent value) authStateChanged,
    required TResult Function(ResetEvent value) reset,
  }) {
    return deleteAccount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(SendOtpEvent value)? sendOtp,
    TResult? Function(VerifyOtpEvent value)? verifyOtp,
    TResult? Function(SignOutEvent value)? signOut,
    TResult? Function(DeleteAccountEvent value)? deleteAccount,
    TResult? Function(AuthStateChangedEvent value)? authStateChanged,
    TResult? Function(ResetEvent value)? reset,
  }) {
    return deleteAccount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(SendOtpEvent value)? sendOtp,
    TResult Function(VerifyOtpEvent value)? verifyOtp,
    TResult Function(SignOutEvent value)? signOut,
    TResult Function(DeleteAccountEvent value)? deleteAccount,
    TResult Function(AuthStateChangedEvent value)? authStateChanged,
    TResult Function(ResetEvent value)? reset,
    required TResult orElse(),
  }) {
    if (deleteAccount != null) {
      return deleteAccount(this);
    }
    return orElse();
  }
}

abstract class DeleteAccountEvent implements AuthEvent {
  const factory DeleteAccountEvent() = _$DeleteAccountEventImpl;
}

/// @nodoc
abstract class _$$AuthStateChangedEventImplCopyWith<$Res> {
  factory _$$AuthStateChangedEventImplCopyWith(
          _$AuthStateChangedEventImpl value,
          $Res Function(_$AuthStateChangedEventImpl) then) =
      __$$AuthStateChangedEventImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AuthStateChangedEventImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$AuthStateChangedEventImpl>
    implements _$$AuthStateChangedEventImplCopyWith<$Res> {
  __$$AuthStateChangedEventImplCopyWithImpl(_$AuthStateChangedEventImpl _value,
      $Res Function(_$AuthStateChangedEventImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$AuthStateChangedEventImpl implements AuthStateChangedEvent {
  const _$AuthStateChangedEventImpl();

  @override
  String toString() {
    return 'AuthEvent.authStateChanged()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthStateChangedEventImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String phoneNumber, String countryCode, int? forceResendingToken)
        sendOtp,
    required TResult Function(String verificationId, String smsCode) verifyOtp,
    required TResult Function() signOut,
    required TResult Function() deleteAccount,
    required TResult Function() authStateChanged,
    required TResult Function() reset,
  }) {
    return authStateChanged();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String phoneNumber, String countryCode, int? forceResendingToken)?
        sendOtp,
    TResult? Function(String verificationId, String smsCode)? verifyOtp,
    TResult? Function()? signOut,
    TResult? Function()? deleteAccount,
    TResult? Function()? authStateChanged,
    TResult? Function()? reset,
  }) {
    return authStateChanged?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String phoneNumber, String countryCode, int? forceResendingToken)?
        sendOtp,
    TResult Function(String verificationId, String smsCode)? verifyOtp,
    TResult Function()? signOut,
    TResult Function()? deleteAccount,
    TResult Function()? authStateChanged,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (authStateChanged != null) {
      return authStateChanged();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(SendOtpEvent value) sendOtp,
    required TResult Function(VerifyOtpEvent value) verifyOtp,
    required TResult Function(SignOutEvent value) signOut,
    required TResult Function(DeleteAccountEvent value) deleteAccount,
    required TResult Function(AuthStateChangedEvent value) authStateChanged,
    required TResult Function(ResetEvent value) reset,
  }) {
    return authStateChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(SendOtpEvent value)? sendOtp,
    TResult? Function(VerifyOtpEvent value)? verifyOtp,
    TResult? Function(SignOutEvent value)? signOut,
    TResult? Function(DeleteAccountEvent value)? deleteAccount,
    TResult? Function(AuthStateChangedEvent value)? authStateChanged,
    TResult? Function(ResetEvent value)? reset,
  }) {
    return authStateChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(SendOtpEvent value)? sendOtp,
    TResult Function(VerifyOtpEvent value)? verifyOtp,
    TResult Function(SignOutEvent value)? signOut,
    TResult Function(DeleteAccountEvent value)? deleteAccount,
    TResult Function(AuthStateChangedEvent value)? authStateChanged,
    TResult Function(ResetEvent value)? reset,
    required TResult orElse(),
  }) {
    if (authStateChanged != null) {
      return authStateChanged(this);
    }
    return orElse();
  }
}

abstract class AuthStateChangedEvent implements AuthEvent {
  const factory AuthStateChangedEvent() = _$AuthStateChangedEventImpl;
}

/// @nodoc
abstract class _$$ResetEventImplCopyWith<$Res> {
  factory _$$ResetEventImplCopyWith(
          _$ResetEventImpl value, $Res Function(_$ResetEventImpl) then) =
      __$$ResetEventImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ResetEventImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$ResetEventImpl>
    implements _$$ResetEventImplCopyWith<$Res> {
  __$$ResetEventImplCopyWithImpl(
      _$ResetEventImpl _value, $Res Function(_$ResetEventImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ResetEventImpl implements ResetEvent {
  const _$ResetEventImpl();

  @override
  String toString() {
    return 'AuthEvent.reset()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ResetEventImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String phoneNumber, String countryCode, int? forceResendingToken)
        sendOtp,
    required TResult Function(String verificationId, String smsCode) verifyOtp,
    required TResult Function() signOut,
    required TResult Function() deleteAccount,
    required TResult Function() authStateChanged,
    required TResult Function() reset,
  }) {
    return reset();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String phoneNumber, String countryCode, int? forceResendingToken)?
        sendOtp,
    TResult? Function(String verificationId, String smsCode)? verifyOtp,
    TResult? Function()? signOut,
    TResult? Function()? deleteAccount,
    TResult? Function()? authStateChanged,
    TResult? Function()? reset,
  }) {
    return reset?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String phoneNumber, String countryCode, int? forceResendingToken)?
        sendOtp,
    TResult Function(String verificationId, String smsCode)? verifyOtp,
    TResult Function()? signOut,
    TResult Function()? deleteAccount,
    TResult Function()? authStateChanged,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(SendOtpEvent value) sendOtp,
    required TResult Function(VerifyOtpEvent value) verifyOtp,
    required TResult Function(SignOutEvent value) signOut,
    required TResult Function(DeleteAccountEvent value) deleteAccount,
    required TResult Function(AuthStateChangedEvent value) authStateChanged,
    required TResult Function(ResetEvent value) reset,
  }) {
    return reset(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(SendOtpEvent value)? sendOtp,
    TResult? Function(VerifyOtpEvent value)? verifyOtp,
    TResult? Function(SignOutEvent value)? signOut,
    TResult? Function(DeleteAccountEvent value)? deleteAccount,
    TResult? Function(AuthStateChangedEvent value)? authStateChanged,
    TResult? Function(ResetEvent value)? reset,
  }) {
    return reset?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(SendOtpEvent value)? sendOtp,
    TResult Function(VerifyOtpEvent value)? verifyOtp,
    TResult Function(SignOutEvent value)? signOut,
    TResult Function(DeleteAccountEvent value)? deleteAccount,
    TResult Function(AuthStateChangedEvent value)? authStateChanged,
    TResult Function(ResetEvent value)? reset,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset(this);
    }
    return orElse();
  }
}

abstract class ResetEvent implements AuthEvent {
  const factory ResetEvent() = _$ResetEventImpl;
}
