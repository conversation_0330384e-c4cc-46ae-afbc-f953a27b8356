import 'package:freezed_annotation/freezed_annotation.dart';

import '../../domain/entities/auth_user.dart';

part 'auth_state.freezed.dart';

/// Authentication states
enum AuthStatus {
  initial,
  loading,
  otpSent,
  authenticated,
  unauthenticated,
  error,
}

/// State for the authentication BLoC
@freezed
class AuthState with _$AuthState {
  const factory AuthState({
    @Default(AuthStatus.initial) AuthStatus status,
    AuthUser? user,
    String? verificationId,
    int? forceResendingToken,
    String? errorMessage,
    @Default(false) bool canResendOtp,
  }) = _AuthState;
  
  const AuthState._();
  
  /// Check if user is authenticated
  bool get isAuthenticated => status == AuthStatus.authenticated && user != null;
  
  /// Check if loading
  bool get isLoading => status == AuthStatus.loading;
  
  /// Check if OTP was sent
  bool get isOtpSent => status == AuthStatus.otpSent && verificationId != null;
  
  /// Check if there's an error
  bool get hasError => status == AuthStatus.error && errorMessage != null;
}
