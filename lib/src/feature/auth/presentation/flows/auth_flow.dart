import 'package:flow_builder/flow_builder.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/di/injection.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_state.dart';
import '../screens/phone_registration_screen.dart';
import '../screens/phone_otp_screen.dart';

/// Authentication flow states
enum AuthFlowState {
  phoneRegistration,
  otpVerification,
  authenticated,
}

/// Authentication flow data
class AuthFlowData {
  const AuthFlowData({
    required this.state,
    this.verificationId,
    this.phoneNumber,
    this.countryCode,
  });

  final AuthFlowState state;
  final String? verificationId;
  final String? phoneNumber;
  final String? countryCode;

  AuthFlowData copyWith({
    AuthFlowState? state,
    String? verificationId,
    String? phoneNumber,
    String? countryCode,
  }) {
    return AuthFlowData(
      state: state ?? this.state,
      verificationId: verificationId ?? this.verificationId,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      countryCode: countryCode ?? this.countryCode,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthFlowData &&
        other.state == state &&
        other.verificationId == verificationId &&
        other.phoneNumber == phoneNumber &&
        other.countryCode == countryCode;
  }

  @override
  int get hashCode {
    return state.hashCode ^
        verificationId.hashCode ^
        phoneNumber.hashCode ^
        countryCode.hashCode;
  }
}

/// Authentication flow widget using flow_builder
class AuthFlow extends StatelessWidget {
  const AuthFlow({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<AuthBloc>(),
      child: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, authState) {
          return FlowBuilder<AuthFlowData>(
            state: _mapAuthStateToFlowData(authState),
            onGeneratePages: _onGeneratePages,
          );
        },
      ),
    );
  }

  /// Map AuthState to AuthFlowData
  AuthFlowData _mapAuthStateToFlowData(AuthState authState) {
    switch (authState.status) {
      case AuthStatus.initial:
      case AuthStatus.loading:
      case AuthStatus.error:
        return const AuthFlowData(state: AuthFlowState.phoneRegistration);
      
      case AuthStatus.otpSent:
        return AuthFlowData(
          state: AuthFlowState.otpVerification,
          verificationId: authState.verificationId,
        );
      
      case AuthStatus.authenticated:
        return const AuthFlowData(state: AuthFlowState.authenticated);
      
      case AuthStatus.unauthenticated:
        return const AuthFlowData(state: AuthFlowState.phoneRegistration);
    }
  }

  /// Generate pages based on flow state
  List<Page> _onGeneratePages(
    AuthFlowData flowData,
    List<Page<dynamic>> pages,
  ) {
    switch (flowData.state) {
      case AuthFlowState.phoneRegistration:
        return [
          const MaterialPage(
            key: ValueKey('phone_registration'),
            child: PhoneRegistrationScreen(),
          ),
        ];

      case AuthFlowState.otpVerification:
        return [
          const MaterialPage(
            key: ValueKey('phone_registration'),
            child: PhoneRegistrationScreen(),
          ),
          MaterialPage(
            key: const ValueKey('phone_otp'),
            child: PhoneOtpScreen(
              verificationId: flowData.verificationId,
            ),
          ),
        ];

      case AuthFlowState.authenticated:
        // This should trigger navigation to main app
        // In a real app, this would be handled by a higher-level flow
        return [
          const MaterialPage(
            key: ValueKey('authenticated'),
            child: Scaffold(
              body: Center(
                child: Text('Authentication Successful!'),
              ),
            ),
          ),
        ];
    }
  }
}
