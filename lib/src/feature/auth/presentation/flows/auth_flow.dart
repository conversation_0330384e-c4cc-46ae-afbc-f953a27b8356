import 'package:flow_builder/flow_builder.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/di/injection.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_state.dart';
import '../screens/phone_otp_screen.dart';
import '../screens/phone_registration_screen.dart';

/// Authentication flow states
enum AuthFlowState {
  phoneRegistration,
  otpVerification,
  authenticated,
  profileSetup,
  completed,
}

/// Authentication flow data with comprehensive state management
class AuthFlowData {
  const AuthFlowData({
    required this.state,
    this.verificationId,
    this.phoneNumber,
    this.countryCode,
    this.user,
    this.errorMessage,
    this.canResendOtp = false,
    this.isLoading = false,
  });

  final AuthFlowState state;
  final String? verificationId;
  final String? phoneNumber;
  final String? countryCode;
  final dynamic user; // AuthUser from domain
  final String? errorMessage;
  final bool canResendOtp;
  final bool isLoading;

  AuthFlowData copyWith({
    AuthFlowState? state,
    String? verificationId,
    String? phoneNumber,
    String? countryCode,
    dynamic user,
    String? errorMessage,
    bool? canResendOtp,
    bool? isLoading,
  }) {
    return AuthFlowData(
      state: state ?? this.state,
      verificationId: verificationId ?? this.verificationId,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      countryCode: countryCode ?? this.countryCode,
      user: user ?? this.user,
      errorMessage: errorMessage ?? this.errorMessage,
      canResendOtp: canResendOtp ?? this.canResendOtp,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthFlowData &&
        other.state == state &&
        other.verificationId == verificationId &&
        other.phoneNumber == phoneNumber &&
        other.countryCode == countryCode &&
        other.user == user &&
        other.errorMessage == errorMessage &&
        other.canResendOtp == canResendOtp &&
        other.isLoading == isLoading;
  }

  @override
  int get hashCode {
    return state.hashCode ^
        verificationId.hashCode ^
        phoneNumber.hashCode ^
        countryCode.hashCode ^
        user.hashCode ^
        errorMessage.hashCode ^
        canResendOtp.hashCode ^
        isLoading.hashCode;
  }
}

/// Authentication flow widget using flow_builder
class AuthFlow extends StatelessWidget {
  const AuthFlow({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<AuthBloc>(),
      child: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, authState) {
          return FlowBuilder<AuthFlowData>(
            state: _mapAuthStateToFlowData(authState),
            onGeneratePages: _onGeneratePages,
          );
        },
      ),
    );
  }

  /// Map AuthState to AuthFlowData with comprehensive state mapping
  AuthFlowData _mapAuthStateToFlowData(AuthState authState) {
    switch (authState.status) {
      case AuthStatus.initial:
        return const AuthFlowData(
          state: AuthFlowState.phoneRegistration,
          isLoading: false,
        );

      case AuthStatus.loading:
        return const AuthFlowData(
          state: AuthFlowState.phoneRegistration,
          isLoading: true,
        );

      case AuthStatus.error:
        return AuthFlowData(
          state: AuthFlowState.phoneRegistration,
          errorMessage: authState.errorMessage,
          isLoading: false,
        );

      case AuthStatus.otpSent:
        return AuthFlowData(
          state: AuthFlowState.otpVerification,
          verificationId: authState.verificationId,
          canResendOtp: authState.canResendOtp,
          isLoading: false,
        );

      case AuthStatus.authenticated:
        // Check if user needs profile setup
        if (authState.user != null) {
          // In a real app, you might check if user profile is complete
          // For now, we'll go directly to completed
          return AuthFlowData(
            state: AuthFlowState.completed,
            user: authState.user,
            isLoading: false,
          );
        }
        return const AuthFlowData(
          state: AuthFlowState.authenticated,
          isLoading: false,
        );

      case AuthStatus.unauthenticated:
        return const AuthFlowData(
          state: AuthFlowState.phoneRegistration,
          isLoading: false,
        );
    }
  }

  /// Generate pages based on flow state with comprehensive flow management
  List<Page> _onGeneratePages(
    AuthFlowData flowData,
    List<Page<dynamic>> pages,
  ) {
    switch (flowData.state) {
      case AuthFlowState.phoneRegistration:
        return [
          MaterialPage(
            key: const ValueKey('phone_registration'),
            child: _buildPhoneRegistrationWithState(flowData),
          ),
        ];

      case AuthFlowState.otpVerification:
        return [
          MaterialPage(
            key: const ValueKey('phone_registration'),
            child: _buildPhoneRegistrationWithState(flowData),
          ),
          MaterialPage(
            key: const ValueKey('phone_otp'),
            child: _buildPhoneOtpWithState(flowData),
          ),
        ];

      case AuthFlowState.authenticated:
        return [
          MaterialPage(
            key: const ValueKey('phone_registration'),
            child: _buildPhoneRegistrationWithState(flowData),
          ),
          MaterialPage(
            key: const ValueKey('phone_otp'),
            child: _buildPhoneOtpWithState(flowData),
          ),
          MaterialPage(
            key: const ValueKey('authenticated'),
            child: _buildAuthenticatedScreen(),
          ),
        ];

      case AuthFlowState.profileSetup:
        return [
          MaterialPage(
            key: const ValueKey('phone_registration'),
            child: _buildPhoneRegistrationWithState(flowData),
          ),
          MaterialPage(
            key: const ValueKey('phone_otp'),
            child: _buildPhoneOtpWithState(flowData),
          ),
          MaterialPage(
            key: const ValueKey('profile_setup'),
            child: _buildProfileSetupScreen(),
          ),
        ];

      case AuthFlowState.completed:
        // This should trigger navigation to main app
        // In a real app, this would be handled by a higher-level flow
        return [
          MaterialPage(
            key: const ValueKey('auth_completed'),
            child: _buildAuthCompletedScreen(),
          ),
        ];
    }
  }

  /// Build phone registration screen with flow state
  Widget _buildPhoneRegistrationWithState(AuthFlowData flowData) {
    return PhoneRegistrationScreen(
      key: ValueKey('phone_reg_${flowData.isLoading}'),
      onNavigateToOtp: () {
        // Flow Builder handles navigation automatically through state changes
        // This callback is just for compatibility
      },
    );
  }

  /// Build phone OTP screen with flow state
  Widget _buildPhoneOtpWithState(AuthFlowData flowData) {
    return PhoneOtpScreen(
      verificationId: flowData.verificationId,
      key: ValueKey('phone_otp_${flowData.canResendOtp}'),
      onNavigateToHome: () {
        // Flow Builder handles navigation automatically through state changes
        // This callback is just for compatibility
      },
    );
  }

  /// Build authenticated screen
  Widget _buildAuthenticatedScreen() {
    return const Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 64,
            ),
            SizedBox(height: 16),
            Text(
              'Authentication Successful!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'You are now authenticated.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build profile setup screen
  Widget _buildProfileSetupScreen() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Complete Your Profile'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.person_add,
              size: 64,
              color: Colors.blue,
            ),
            SizedBox(height: 16),
            Text(
              'Profile Setup',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Complete your profile to continue.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build auth completed screen
  Widget _buildAuthCompletedScreen() {
    return const Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.celebration,
              color: Colors.orange,
              size: 64,
            ),
            SizedBox(height: 16),
            Text(
              'Welcome!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Authentication flow completed successfully.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
