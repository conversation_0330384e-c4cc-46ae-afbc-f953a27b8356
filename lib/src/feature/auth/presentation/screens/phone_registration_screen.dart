import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/assets/assets.gen.dart';
import '../../../../core/theme/sdm_palette.dart';
import '../../../../core/router/sdm_router.dart';
import '../../../../core/di/injection.dart';
import '../../../../shared/shared.dart';
import '../../../analytics/bloc/analytics_cubit.dart';
import '../../../analytics/models/analytics_events.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import '../bloc/auth_state.dart';

/// Phone registration screen using clean architecture
class PhoneRegistrationScreen extends StatefulWidget {
  const PhoneRegistrationScreen({super.key});

  @override
  State<PhoneRegistrationScreen> createState() => _PhoneRegistrationScreenState();
}

class _PhoneRegistrationScreenState extends State<PhoneRegistrationScreen> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _countryCodeController = TextEditingController(text: '+91');

  @override
  void dispose() {
    _phoneController.dispose();
    _countryCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<AuthBloc>(),
      child: BlocListener<AuthBloc, AuthState>(
        listenWhen: (previous, current) => previous.status != current.status,
        listener: (context, state) {
          if (state.status == AuthStatus.otpSent) {
            Navigator.pushNamed(
              context, 
              SdmRouter.phoneOtp,
              arguments: state.verificationId,
            );
          } else if (state.hasError) {
            SdmToast.show(state.errorMessage ?? 'Something went wrong');
          }
        },
        child: BlocBuilder<AuthBloc, AuthState>(
          builder: (context, state) {
            return Scaffold(
              appBar: AppBar(
                foregroundColor: SdmPalette.black,
              ),
              body: ListView(
                physics: const ClampingScrollPhysics(),
                children: [
                  SizedBox(height: 72.h),
                  Assets.sdmImages.registratioIllustration.image(height: 196.h),
                  SizedBox(height: 12.h),
                  Text(
                    'Welcome',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 17.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'Enter your phone number to get started',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: SdmPalette.grey,
                    ),
                  ),
                  SizedBox(height: 48.h),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 24.w),
                    child: Row(
                      children: [
                        // Country code field
                        SizedBox(
                          width: 80.w,
                          child: SdmTextField(
                            controller: _countryCodeController,
                            hintText: '+91',
                            keyboardType: TextInputType.phone,
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(RegExp(r'[+0-9]')),
                            ],
                          ),
                        ),
                        SizedBox(width: 12.w),
                        // Phone number field
                        Expanded(
                          child: SdmTextField(
                            controller: _phoneController,
                            hintText: 'Phone Number',
                            keyboardType: TextInputType.phone,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                              LengthLimitingTextInputFormatter(10),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 48.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ConstrainedBox(
                        constraints: BoxConstraints(
                          minWidth: 130.w,
                          maxWidth: 130.w,
                        ),
                        child: SdmPrimaryCta(
                          onPressed: state.isLoading ? null : () => _sendOtp(context),
                          child: state.isLoading
                              ? const CircularProgressIndicator.adaptive()
                              : Text(
                                  'Send',
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: SdmPalette.white,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  void _sendOtp(BuildContext context) {
    // Validate input
    if (_phoneController.text.trim().isEmpty) {
      SdmToast.show('Please enter your phone number');
      return;
    }

    if (_phoneController.text.trim().length < 10) {
      SdmToast.show('Please enter a valid phone number');
      return;
    }

    // Track analytics
    context.read<AnalyticsCubit>().onTrackAnalyticsEvent(SendOtpClickEvent());

    // Send OTP using clean architecture BLoC
    context.read<AuthBloc>().add(
      AuthEvent.sendOtp(
        phoneNumber: _phoneController.text.trim(),
        countryCode: _countryCodeController.text.trim(),
      ),
    );
  }
}
