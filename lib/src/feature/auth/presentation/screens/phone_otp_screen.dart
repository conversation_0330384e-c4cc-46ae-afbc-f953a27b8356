import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pinput/pinput.dart';
import 'package:purchases_flutter/purchases_flutter.dart';

import '../../../../core/assets/assets.gen.dart';
import '../../../../core/theme/sdm_palette.dart';
import '../../../../core/router/sdm_router.dart';
import '../../../../core/di/injection.dart';
import '../../../../shared/shared.dart';
import '../../../analytics/bloc/analytics_cubit.dart';
import '../../../analytics/models/analytics_events.dart';
import '../../../purchases/bloc/purchases_cubit.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import '../bloc/auth_state.dart';

/// Phone OTP verification screen using clean architecture
class PhoneOtpScreen extends StatefulWidget {
  const PhoneOtpScreen({super.key});

  @override
  State<PhoneOtpScreen> createState() => _PhoneOtpScreenState();
}

class _PhoneOtpScreenState extends State<PhoneOtpScreen> {
  final TextEditingController _otpController = TextEditingController();
  String? _verificationId;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Get verification ID from route arguments
    final args = ModalRoute.of(context)?.settings.arguments;
    if (args is String) {
      _verificationId = args;
    }
  }

  @override
  void dispose() {
    _otpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<AuthBloc>(),
      child: BlocListener<AuthBloc, AuthState>(
        listenWhen: (previous, current) => 
            previous.status != current.status || previous.user != current.user,
        listener: (context, state) async {
          if (state.status == AuthStatus.authenticated && state.user != null) {
            debugPrint('OTP Screen: User authenticated, navigating to home');
            
            // Configure purchases if available
            if (await Purchases.isConfigured) {
              await Purchases.logIn(state.user!.uid);
            }
            
            // Small delay for UserSessionListener to process
            await Future.delayed(const Duration(milliseconds: 500));
            
            if (mounted) {
              Navigator.pushNamedAndRemoveUntil(
                context,
                SdmRouter.home,
                (route) => false,
              );
            }
          } else if (state.hasError) {
            SdmToast.show(state.errorMessage ?? 'Something went wrong');
          }
        },
        child: BlocBuilder<AuthBloc, AuthState>(
          builder: (context, state) {
            return Scaffold(
              appBar: AppBar(
                title: const Text('Verify'),
                foregroundColor: SdmPalette.black,
              ),
              body: ListView(
                physics: const ClampingScrollPhysics(),
                children: [
                  SizedBox(height: 72.h),
                  Assets.sdmImages.otpIllustration.image(height: 196.h),
                  SizedBox(height: 12.h),
                  Text(
                    'Verification',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 17.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'Enter the OTP sent to your phone',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: SdmPalette.grey,
                    ),
                  ),
                  SizedBox(height: 48.h),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 24.w),
                    child: Pinput(
                      controller: _otpController,
                      length: 6,
                      defaultPinTheme: PinTheme(
                        width: 56.w,
                        height: 56.h,
                        textStyle: TextStyle(
                          fontSize: 20.sp,
                          fontWeight: FontWeight.w600,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(color: SdmPalette.grey),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      focusedPinTheme: PinTheme(
                        width: 56.w,
                        height: 56.h,
                        textStyle: TextStyle(
                          fontSize: 20.sp,
                          fontWeight: FontWeight.w600,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(color: SdmPalette.primary),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      onCompleted: (pin) => _verifyOtp(context, pin),
                    ),
                  ),
                  SizedBox(height: 24.h),
                  if (state.canResendOtp)
                    TextButton(
                      onPressed: () => _resendOtp(context),
                      child: Text(
                        'Resend OTP',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: SdmPalette.primary,
                        ),
                      ),
                    ),
                  SizedBox(height: 48.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ConstrainedBox(
                        constraints: BoxConstraints(
                          minWidth: 130.w,
                          maxWidth: 130.w,
                        ),
                        child: SdmPrimaryCta(
                          onPressed: state.isLoading ? null : () => _verifyOtp(context, _otpController.text),
                          child: state.isLoading
                              ? const CircularProgressIndicator.adaptive()
                              : Text(
                                  'Verify',
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: SdmPalette.white,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  void _verifyOtp(BuildContext context, String otp) {
    if (otp.trim().isEmpty || otp.length < 6) {
      SdmToast.show('Please enter the complete OTP');
      return;
    }

    if (_verificationId == null) {
      SdmToast.show('Verification ID not found. Please try again.');
      Navigator.pop(context);
      return;
    }

    // Track analytics
    context.read<AnalyticsCubit>().onTrackAnalyticsEvent(VerifyOtpClickEvent());

    // Verify OTP using clean architecture BLoC
    context.read<AuthBloc>().add(
      AuthEvent.verifyOtp(
        verificationId: _verificationId!,
        smsCode: otp.trim(),
      ),
    );
  }

  void _resendOtp(BuildContext context) {
    // Track analytics
    context.read<AnalyticsCubit>().onTrackAnalyticsEvent(ResendOtpClickEvent());
    
    // Navigate back to phone registration to resend OTP
    Navigator.pop(context);
    SdmToast.show('Please enter your phone number again to resend OTP');
  }
}
