import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/di/injection.dart';
import '../../../../core/theme/sdm_palette.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_state.dart';
import '../flows/auth_flow.dart';

/// Demo screen showcasing Flow Builder implementation
class AuthFlowDemoScreen extends StatelessWidget {
  const AuthFlowDemoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<AuthBloc>(),
      child: const _AuthFlowDemoView(),
    );
  }
}

class _AuthFlowDemoView extends StatelessWidget {
  const _AuthFlowDemoView();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Flow Builder Demo'),
        backgroundColor: SdmPalette.primary,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Flow Builder Status Panel
          _buildStatusPanel(context),

          // Flow Builder Implementation
          Expanded(
            child: AuthFlow(),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusPanel(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        return Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          margin: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: SdmPalette.white,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: SdmPalette.textColorGrey2,
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.account_tree,
                    color: SdmPalette.primary,
                    size: 24.sp,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'Flow Builder Status',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: SdmPalette.black,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12.h),

              // Current State
              _buildStatusItem(
                'Current State',
                _getStatusText(state.status),
                _getStatusColor(state.status),
              ),

              // Flow Features
              _buildStatusItem(
                'Auto Route',
                'Type-safe navigation enabled',
                Colors.green,
              ),

              _buildStatusItem(
                'Flow Builder',
                'Complex flow management active',
                Colors.blue,
              ),

              if (state.verificationId != null)
                _buildStatusItem(
                  'Verification ID',
                  '${state.verificationId!.substring(0, 20)}...',
                  Colors.orange,
                ),

              if (state.user != null)
                _buildStatusItem(
                  'User',
                  'Authenticated',
                  Colors.green,
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatusItem(String label, String value, Color color) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        children: [
          Container(
            width: 8.w,
            height: 8.h,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 8.w),
          Text(
            '$label: ',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: SdmPalette.textColorSecondary,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14.sp,
                color: SdmPalette.textColorPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusText(AuthStatus status) {
    switch (status) {
      case AuthStatus.initial:
        return 'Initial - Ready for phone input';
      case AuthStatus.loading:
        return 'Loading - Processing request';
      case AuthStatus.otpSent:
        return 'OTP Sent - Waiting for verification';
      case AuthStatus.authenticated:
        return 'Authenticated - User logged in';
      case AuthStatus.error:
        return 'Error - Something went wrong';
      case AuthStatus.unauthenticated:
        return 'Unauthenticated - User logged out';
    }
  }

  Color _getStatusColor(AuthStatus status) {
    switch (status) {
      case AuthStatus.initial:
        return Colors.grey;
      case AuthStatus.loading:
        return Colors.orange;
      case AuthStatus.otpSent:
        return Colors.blue;
      case AuthStatus.authenticated:
        return Colors.green;
      case AuthStatus.error:
        return Colors.red;
      case AuthStatus.unauthenticated:
        return Colors.grey;
    }
  }
}
