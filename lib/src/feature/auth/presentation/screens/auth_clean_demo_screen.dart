import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/di/injection.dart';
import '../../../../core/theme/sdm_palette.dart';
import '../../../../shared/shared.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import '../bloc/auth_state.dart';
import '../flows/auth_flow.dart';
import 'phone_registration_screen.dart';

/// Demo screen to showcase the clean architecture authentication
/// This can be accessed for testing the new implementation
class AuthCleanDemoScreen extends StatelessWidget {
  const AuthCleanDemoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<AuthBloc>(),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Clean Architecture Auth Demo'),
          backgroundColor: SdmPalette.primary,
          foregroundColor: SdmPalette.white,
        ),
        body: BlocBuilder<AuthBloc, AuthState>(
          builder: (context, state) {
            return Padding(
              padding: EdgeInsets.all(24.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SizedBox(height: 32.h),
                  Text(
                    'Clean Architecture Authentication',
                    style: TextStyle(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    'This demonstrates the new clean architecture implementation with:',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: SdmPalette.textColorGrey,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  _buildFeatureItem(
                      '✅ Domain Layer with entities and use cases'),
                  _buildFeatureItem(
                      '✅ Data Layer with Firebase implementation'),
                  _buildFeatureItem('✅ Presentation Layer with BLoC'),
                  _buildFeatureItem('✅ Dependency Injection with GetIt'),
                  _buildFeatureItem(
                      '✅ Backend flexibility for future migration'),
                  SizedBox(height: 32.h),
                  Text(
                    'Current Auth Status: ${state.status.name}',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                      color: state.isAuthenticated
                          ? Colors.green
                          : SdmPalette.textColorGrey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  if (state.user != null) ...[
                    SizedBox(height: 16.h),
                    Text(
                      'User: ${state.user!.phoneNumber}',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: SdmPalette.textColorGrey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                  SizedBox(height: 48.h),
                  if (!state.isAuthenticated) ...[
                    SdmPrimaryCta(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                const PhoneRegistrationScreen(),
                          ),
                        );
                      },
                      child: Text(
                        'Test Auto Route Navigation',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: SdmPalette.white,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                    SizedBox(height: 16.h),
                    SdmPrimaryCta(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const AuthFlow(),
                          ),
                        );
                      },
                      child: Text(
                        'Test Flow Builder Navigation',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: SdmPalette.white,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ] else ...[
                    SdmPrimaryCta(
                      onPressed: () {
                        context.read<AuthBloc>().add(const AuthEvent.signOut());
                      },
                      child: Text(
                        'Sign Out',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: SdmPalette.white,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 14.sp,
          color: SdmPalette.textColorGrey,
        ),
      ),
    );
  }
}
