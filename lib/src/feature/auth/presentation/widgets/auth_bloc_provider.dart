import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/di/injection.dart';
import '../bloc/auth_bloc.dart';

/// Provides AuthBloc to the widget tree
/// This ensures the same AuthBloc instance is used across authentication screens
class AuthBlocProvider extends StatelessWidget {
  const AuthBlocProvider({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<AuthBloc>(),
      child: child,
    );
  }
}

/// Provides AuthBloc with a specific instance
/// Useful for testing or when you need to pass a specific bloc instance
class AuthBlocProviderValue extends StatelessWidget {
  const AuthBlocProviderValue({
    super.key,
    required this.authBloc,
    required this.child,
  });

  final AuthBloc authBloc;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: authBloc,
      child: child,
    );
  }
}
