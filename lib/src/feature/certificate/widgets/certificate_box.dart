import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;
import 'package:shridattmandir/src/shared/shared.dart' show SdmNetworkImage;

class CertificateBox extends StatelessWidget {
  const CertificateBox({
    super.key,
    required this.title,
    this.thumbnailURL,
    this.description,
    this.url,
  });
  final String? thumbnailURL;

  final String? title;
  final String? description;
  final String? url;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.0),
          color: SdmPalette.white,
          boxShadow: [
            BoxShadow(
              color: SdmPalette.black29,
              offset: Offset(0, 2.h),
              blurRadius: 5.r,
            )
          ]),
      padding: EdgeInsets.only(left: 10.w, right: 10.w, top: 10.h),
      margin: EdgeInsets.only(bottom: 25.h, left: 20.w, right: 20.w),
      child: <PERSON>umn(
        children: <Widget>[
          SdmNetworkImage(
            url: thumbnailURL,
            errorWidget: const Icon(Icons.error),
          ),
          SizedBox(
            height: 5.w,
          ),
          Text(
            title ?? '',
            textAlign: TextAlign.center,
            softWrap: true,
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: SdmPalette.black,
            ),
          ),
          SizedBox(
            height: 5.h,
          ),
        ],
      ),
    );
  }
}
