import 'package:flutter/widgets.dart';
import 'package:shridattmandir/src/core/core.dart' show RestClient;

class AccredibleRepository extends RestClient {
  static const String _baseUrl = 'https://api.accredible.com/v1/';
  static const String _certificateUrl = '${_baseUrl}all_credentials?';

  static const Map<String, String> _headers = {
    "Content-Type": "application/json",
    "Authorization": "Token token=44574afdfd0eb1564e2d7b597da909fa"
  };

  Future<String> getCertificate(String email) async {
    final response = await get(
      '${_certificateUrl}email=$email',
      headers: _headers,
    );
    switch (response.statusCode) {
      case 201:
      case 204:
      case 200:
        return response.body;
      default:
        debugPrint(response.body);
        throw Exception(
          'Failed to load certificate',
        );
    }
  }
}
