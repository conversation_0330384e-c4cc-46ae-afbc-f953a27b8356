abstract class AccredibleCredentialsException implements Exception {
  const AccredibleCredentialsException(this.error, this.stackTrace);

  final Object error;
  final StackTrace stackTrace;
}

class EmailInvalidException extends AccredibleCredentialsException {
  const EmailInvalidException(super.error, super.stackTrace);
}

class NoCredentialsFoundException extends AccredibleCredentialsException {
  const NoCredentialsFoundException(super.error, super.stackTrace);
}
