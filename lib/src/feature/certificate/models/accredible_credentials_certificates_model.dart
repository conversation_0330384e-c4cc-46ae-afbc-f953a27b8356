// To parse this JSON data, do
//
//     final accredibleCredentialsCertificates = accredibleCredentialsCertificatesFrom<PERSON>son(jsonString);

import 'dart:convert';

AccredibleCredentialsCertificates accredibleCredentialsCertificatesFrom<PERSON>son(
        String str) =>
    AccredibleCredentialsCertificates.fromJson(json.decode(str));

String accredibleCredentialsCertificatesToJson(
        AccredibleCredentialsCertificates data) =>
    json.encode(data.toJson());

class AccredibleCredentialsCertificates {
  final List<Credential>? credentials;
  final Meta? meta;

  AccredibleCredentialsCertificates({
    this.credentials,
    this.meta,
  });

  factory AccredibleCredentialsCertificates.fromJson(
          Map<String, dynamic> json) =>
      AccredibleCredentialsCertificates(
        credentials: json["credentials"] == null
            ? []
            : List<Credential>.from(
                json["credentials"]!.map((x) => Credential.fromJson(x))),
        meta: json["meta"] == null ? null : Meta.fromJson(json["meta"]),
      );

  Map<String, dynamic> toJson() => {
        "credentials": credentials == null
            ? []
            : List<dynamic>.from(credentials!.map((x) => x.toJson())),
        "meta": meta?.toJson(),
      };
}

class Credential {
  final int? id;
  final String? name;
  final String? description;
  final bool? approve;
  final NextPage? grade;
  final bool? complete;
  final DateTime? issuedOn;
  final NextPage? allowSupplementalReferences;
  final NextPage? allowSupplementalEvidence;
  final String? courseLink;
  final NextPage? customAttributes;
  final NextPage? expiredOn;
  final String? groupName;
  final int? groupId;
  final String? url;
  final String? encodedId;
  final bool? private;
  final int? accredibleInternalId;
  final String? seoImage;
  final DateTime? updatedAt;
  final Certificate? certificate;
  final Badge? badge;
  final NextPage? metaData;
  final String? uuid;
  final Issuer? issuer;

  Credential({
    this.id,
    this.name,
    this.description,
    this.approve,
    this.grade,
    this.complete,
    this.issuedOn,
    this.allowSupplementalReferences,
    this.allowSupplementalEvidence,
    this.courseLink,
    this.customAttributes,
    this.expiredOn,
    this.groupName,
    this.groupId,
    this.url,
    this.encodedId,
    this.private,
    this.accredibleInternalId,
    this.seoImage,
    this.updatedAt,
    this.certificate,
    this.badge,
    this.metaData,
    this.uuid,
    this.issuer,
  });

  factory Credential.fromJson(Map<String, dynamic> json) => Credential(
        id: json["id"],
        name: json["name"],
        description: json["description"],
        approve: json["approve"],
        grade: json["grade"] == null ? null : NextPage.fromJson(json["grade"]),
        complete: json["complete"],
        issuedOn: json["issued_on"] == null
            ? null
            : DateTime.parse(json["issued_on"]),
        allowSupplementalReferences:
            json["allow_supplemental_references"] == null
                ? null
                : NextPage.fromJson(json["allow_supplemental_references"]),
        allowSupplementalEvidence: json["allow_supplemental_evidence"] == null
            ? null
            : NextPage.fromJson(json["allow_supplemental_evidence"]),
        courseLink: json["course_link"],
        customAttributes: json["custom_attributes"] == null
            ? null
            : NextPage.fromJson(json["custom_attributes"]),
        expiredOn: json["expired_on"] == null
            ? null
            : NextPage.fromJson(json["expired_on"]),
        groupName: json["group_name"],
        groupId: json["group_id"],
        url: json["url"],
        encodedId: json["encoded_id"],
        private: json["private"],
        accredibleInternalId: json["accredible_internal_id"],
        seoImage: json["seo_image"],
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        certificate: json["certificate"] == null
            ? null
            : Certificate.fromJson(json["certificate"]),
        badge: json["badge"] == null ? null : Badge.fromJson(json["badge"]),
        metaData: json["meta_data"] == null
            ? null
            : NextPage.fromJson(json["meta_data"]),
        uuid: json["uuid"],
        issuer: json["issuer"] == null ? null : Issuer.fromJson(json["issuer"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "description": description,
        "approve": approve,
        "grade": grade?.toJson(),
        "complete": complete,
        "issued_on":
            "${issuedOn!.year.toString().padLeft(4, '0')}-${issuedOn!.month.toString().padLeft(2, '0')}-${issuedOn!.day.toString().padLeft(2, '0')}",
        "allow_supplemental_references": allowSupplementalReferences?.toJson(),
        "allow_supplemental_evidence": allowSupplementalEvidence?.toJson(),
        "course_link": courseLink,
        "custom_attributes": customAttributes?.toJson(),
        "expired_on": expiredOn?.toJson(),
        "group_name": groupName,
        "group_id": groupId,
        "url": url,
        "encoded_id": encodedId,
        "private": private,
        "accredible_internal_id": accredibleInternalId,
        "seo_image": seoImage,
        "updated_at": updatedAt?.toIso8601String(),
        "certificate": certificate?.toJson(),
        "badge": badge?.toJson(),
        "meta_data": metaData?.toJson(),
        "uuid": uuid,
        "issuer": issuer?.toJson(),
      };
}

class NextPage {
  NextPage();

  factory NextPage.fromJson(Map<String, dynamic> json) => NextPage();

  Map<String, dynamic> toJson() => {};
}

class Badge {
  final BadgeImage? image;

  Badge({
    this.image,
  });

  factory Badge.fromJson(Map<String, dynamic> json) => Badge(
        image:
            json["image"] == null ? null : BadgeImage.fromJson(json["image"]),
      );

  Map<String, dynamic> toJson() => {
        "image": image?.toJson(),
      };
}

class BadgeImage {
  final dynamic preview;

  BadgeImage({
    this.preview,
  });

  factory BadgeImage.fromJson(Map<String, dynamic> json) => BadgeImage(
        preview: json["preview"],
      );

  Map<String, dynamic> toJson() => {
        "preview": preview,
      };
}

class Certificate {
  final CertificateImage? image;

  Certificate({
    this.image,
  });

  factory Certificate.fromJson(Map<String, dynamic> json) => Certificate(
        image: json["image"] == null
            ? null
            : CertificateImage.fromJson(json["image"]),
      );

  Map<String, dynamic> toJson() => {
        "image": image?.toJson(),
      };
}

class CertificateImage {
  final String? preview;

  CertificateImage({
    this.preview,
  });

  factory CertificateImage.fromJson(Map<String, dynamic> json) =>
      CertificateImage(
        preview: json["preview"],
      );

  Map<String, dynamic> toJson() => {
        "preview": preview,
      };
}

class Issuer {
  final String? name;
  final NextPage? description;
  final String? url;

  Issuer({
    this.name,
    this.description,
    this.url,
  });

  factory Issuer.fromJson(Map<String, dynamic> json) => Issuer(
        name: json["name"],
        description: json["description"] == null
            ? null
            : NextPage.fromJson(json["description"]),
        url: json["url"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "description": description?.toJson(),
        "url": url,
      };
}

class Meta {
  final int? currentPage;
  final NextPage? nextPage;
  final NextPage? prevPage;
  final int? totalPages;
  final int? totalCount;
  final int? pageSize;

  Meta({
    this.currentPage,
    this.nextPage,
    this.prevPage,
    this.totalPages,
    this.totalCount,
    this.pageSize,
  });

  factory Meta.fromJson(Map<String, dynamic> json) => Meta(
        currentPage: json["current_page"],
        nextPage: json["next_page"] == null
            ? null
            : NextPage.fromJson(json["next_page"]),
        prevPage: json["prev_page"] == null
            ? null
            : NextPage.fromJson(json["prev_page"]),
        totalPages: json["total_pages"],
        totalCount: json["total_count"],
        pageSize: json["page_size"],
      );

  Map<String, dynamic> toJson() => {
        "current_page": currentPage,
        "next_page": nextPage?.toJson(),
        "prev_page": prevPage?.toJson(),
        "total_pages": totalPages,
        "total_count": totalCount,
        "page_size": pageSize,
      };
}
