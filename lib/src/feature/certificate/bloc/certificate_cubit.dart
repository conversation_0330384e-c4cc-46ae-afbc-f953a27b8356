import 'dart:convert';
import 'dart:developer';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/core/core.dart' show CubitExt;
import 'package:shridattmandir/src/feature/feature.dart'
    show
        AccredibleCredentialsCertificates,
        AccredibleRepository,
        EmailInvalidException,
        NoCredentialsFoundException;
import 'package:shridattmandir/src/shared/shared.dart' show SdmToast;

part 'certificate_state.dart';

class CertificateCubit extends Cubit<CertificateState> {
  CertificateCubit({
    required AccredibleRepository accredibleRepository,
  })  : _accredibleRepository = accredibleRepository,
        super(const CertificateState());

  final AccredibleRepository _accredibleRepository;

  void onEmailChanged(String value) {
    safeEmit(
      state.copyWith(
        email: value,
      ),
    );
  }

  Future<void> getCertificates() async {
    safeEmit(
      state.copyWith(
        status: CertificateStatus.loading,
      ),
    );

    try {
      if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
          .hasMatch(state.email ?? '')) {
        throw EmailInvalidException(
          'Please enter a valid email address.',
          StackTrace.fromString('Email Exception'),
        );
      }

      final response = await _accredibleRepository.getCertificate(
        state.email!,
      );

      final data = jsonDecode(response);

      final certificates = AccredibleCredentialsCertificates.fromJson(data);

      if (certificates.credentials?.isEmpty ?? false) {
        throw NoCredentialsFoundException(
          'No certificates found for this email address.',
          StackTrace.fromString(
            'No Credentials Exception',
          ),
        );
      }
      safeEmit(
        state.copyWith(
          status: CertificateStatus.success,
          certificates: certificates,
        ),
      );
    } on EmailInvalidException catch (e, stackTrace) {
      inspect(stackTrace);
      safeEmit(
        state.copyWith(
          status: CertificateStatus.error,
        ),
      );

      SdmToast.show(
        e.error.toString(),
      );
    } on NoCredentialsFoundException catch (e, stackTrace) {
      inspect(stackTrace);
      safeEmit(
        state.copyWith(
          status: CertificateStatus.error,
        ),
      );

      SdmToast.show(
        e.error.toString(),
      );
    } catch (e, stackTrace) {
      inspect(stackTrace);
      safeEmit(
        state.copyWith(
          status: CertificateStatus.error,
        ),
      );

      SdmToast.show(
        e.toString(),
      );
    }
  }
}
