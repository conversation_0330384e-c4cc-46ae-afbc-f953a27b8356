part of 'certificate_cubit.dart';

enum CertificateStatus {
  inital,
  loading,
  success,
  error,
}

final class CertificateState extends Equatable {
  const CertificateState({
    this.certificates,
    this.status = CertificateStatus.inital,
    this.email,
  });

  final AccredibleCredentialsCertificates? certificates;
  final CertificateStatus status;
  final String? email;

  CertificateState copyWith({
    AccredibleCredentialsCertificates? certificates,
    CertificateStatus? status,
    String? email,
  }) {
    return CertificateState(
      certificates: certificates ?? this.certificates,
      status: status ?? this.status,
      email: email ?? this.email,
    );
  }

  @override
  List<Object?> get props => [
        certificates,
        status,
        email,
      ];
}
