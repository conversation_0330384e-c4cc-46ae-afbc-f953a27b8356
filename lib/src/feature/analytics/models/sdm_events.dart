import 'package:shridattmandir/src/feature/feature.dart' show AnalyticsEvent;

enum ActionType {
  click,
  load,
  play,
  open,
}

enum FeatureCategory {
  splash,
  login,
  home,
  aboutUs,
  profile,
  music,
  video,
  calendar,
  blogs,
  publications,
  certificates,
  contactUs,
  info,
}

abstract class SdmEvent extends AnalyticsEvent {
  /// {@macro ntg_event}
  SdmEvent({
    required String name,
    required String category,
    required String action,
    required bool nonInteraction,
    String? label,
    Object? value,
    String? hitType,
  }) : super(
          name,
          properties: <String, Object>{
            'eventCategory': category,
            'eventAction': action,
            'nonInteraction': '$nonInteraction',
            if (label != null) 'eventLabel': label,
            if (value != null) 'eventValue': value,
            if (hitType != null) 'hitType': hitType,
          },
        );
}

class SplashLoadEvent extends SdmEvent {
  SplashLoadEvent()
      : super(
          name: 'splash_load',
          category: FeatureCategory.splash.name,
          action: ActionType.load.name,
          nonInteraction: true,
        );
}

class SendOtpClickEvent extends SdmEvent {
  SendOtpClickEvent()
      : super(
          name: 'send_otp_click',
          category: FeatureCategory.login.name,
          action: ActionType.click.name,
          nonInteraction: false,
        );
}

class VerifyOtpClickEvent extends SdmEvent {
  VerifyOtpClickEvent()
      : super(
          name: 'verify_otp_click',
          category: FeatureCategory.login.name,
          action: ActionType.click.name,
          nonInteraction: false,
        );
}

class ResendOtpClickEvent extends SdmEvent {
  ResendOtpClickEvent()
      : super(
          name: 'resend_otp_click',
          category: FeatureCategory.login.name,
          action: ActionType.click.name,
          nonInteraction: false,
        );
}

class HomeLoadEvent extends SdmEvent {
  HomeLoadEvent()
      : super(
          name: 'home_load',
          category: FeatureCategory.home.name,
          action: ActionType.load.name,
          nonInteraction: true,
        );
}

class AboutUsClickEvent extends SdmEvent {
  AboutUsClickEvent()
      : super(
          name: 'about_us_click',
          category: FeatureCategory.aboutUs.name,
          action: ActionType.open.name,
          nonInteraction: false,
        );
}

class ProfileClickEvent extends SdmEvent {
  ProfileClickEvent()
      : super(
          name: 'profile_click',
          category: FeatureCategory.profile.name,
          action: ActionType.open.name,
          nonInteraction: false,
        );
}

class LogoutClickEvent extends SdmEvent {
  LogoutClickEvent()
      : super(
          name: 'logout_click',
          category: FeatureCategory.profile.name,
          action: ActionType.click.name,
          nonInteraction: false,
        );
}

class DeleteAccountClickEvent extends SdmEvent {
  DeleteAccountClickEvent()
      : super(
          name: 'delete_account_click',
          category: FeatureCategory.profile.name,
          action: ActionType.click.name,
          nonInteraction: false,
        );
}

class MusicClickEvent extends SdmEvent {
  MusicClickEvent()
      : super(
          name: 'music_click',
          category: FeatureCategory.music.name,
          action: ActionType.open.name,
          nonInteraction: false,
        );
}

class MusicPaywallSheetOpenEvent extends SdmEvent {
  MusicPaywallSheetOpenEvent()
      : super(
          name: 'music_paywall_sheet_open',
          category: FeatureCategory.music.name,
          action: ActionType.open.name,
          nonInteraction: false,
        );
}

class HasMusicPremiumEvent extends SdmEvent {
  HasMusicPremiumEvent({
    required String userId,
  }) : super(
          name: 'music_premium_enabled',
          category: FeatureCategory.music.name,
          action: ActionType.load.name,
          nonInteraction: false,
          label: userId,
        );
}

class MusicPlayEvent extends SdmEvent {
  MusicPlayEvent({
    required String musicName,
    required String id,
  }) : super(
          name: 'music_play',
          category: FeatureCategory.music.name,
          action: ActionType.play.name,
          nonInteraction: false,
          label: musicName,
          value: id,
        );
}

class VideosClickEvent extends SdmEvent {
  VideosClickEvent()
      : super(
          name: 'videos_click',
          category: FeatureCategory.video.name,
          action: ActionType.open.name,
          nonInteraction: false,
        );
}

class VideosPlayEvent extends SdmEvent {
  VideosPlayEvent({
    required String videoName,
    required String id,
  }) : super(
          name: 'videos_play',
          category: FeatureCategory.video.name,
          action: ActionType.play.name,
          nonInteraction: false,
          label: videoName,
          value: id,
        );
}

class CalendarClickEvent extends SdmEvent {
  CalendarClickEvent()
      : super(
          name: 'calendar_click',
          category: FeatureCategory.calendar.name,
          action: ActionType.open.name,
          nonInteraction: false,
        );
}

class BlogsClickEvent extends SdmEvent {
  BlogsClickEvent()
      : super(
          name: 'blogs_click',
          category: FeatureCategory.blogs.name,
          action: ActionType.open.name,
          nonInteraction: false,
        );
}

class BlogsReadClickEvent extends SdmEvent {
  BlogsReadClickEvent({
    required String blogName,
  }) : super(
          name: 'blogs_read_click',
          category: FeatureCategory.blogs.name,
          action: ActionType.open.name,
          nonInteraction: false,
          label: blogName,
        );
}

class PublicationsClickEvent extends SdmEvent {
  PublicationsClickEvent()
      : super(
          name: 'publications_click',
          category: FeatureCategory.publications.name,
          action: ActionType.open.name,
          nonInteraction: false,
        );
}

class CertificatesClickEvent extends SdmEvent {
  CertificatesClickEvent()
      : super(
          name: 'certificates_click',
          category: FeatureCategory.certificates.name,
          action: ActionType.open.name,
          nonInteraction: false,
        );
}

class ContactUsClickEvent extends SdmEvent {
  ContactUsClickEvent()
      : super(
          name: 'contact_us_click',
          category: FeatureCategory.contactUs.name,
          action: ActionType.open.name,
          nonInteraction: false,
        );
}

class InfoClickEvent extends SdmEvent {
  InfoClickEvent()
      : super(
          name: 'info_click',
          category: FeatureCategory.info.name,
          action: ActionType.open.name,
          nonInteraction: false,
        );
}
