import 'dart:developer';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:shridattmandir/src/feature/feature.dart' show AnalyticsEvent;

class AnalyticsRepository {
  const AnalyticsRepository(FirebaseAnalytics analytics)
      : _analytics = analytics;

  final FirebaseAnalytics _analytics;

  /// Tracks the provided [AnalyticsEvent].
  Future<void> track(AnalyticsEvent event) async {
    try {
      await _analytics.logEvent(
        name: event.name,
        parameters: event.properties,
      );
      log(
        '📢📢📢📢 $event 📢📢📢📢',
      );
    } catch (error) {
      throw Exception('Failed to track analytics event: $error');
    }
  }

  Future<void> setUserId(String? userId) async {
    try {
      await _analytics.setUserId(id: userId);
    } catch (error, stackTrace) {
      throw Exception('Failed to set user id: $error, $stackTrace');
    }
  }
}
