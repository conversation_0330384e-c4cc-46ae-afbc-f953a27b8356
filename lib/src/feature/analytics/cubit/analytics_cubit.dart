import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show AnalyticsEvent, AnalyticsRepository;
import 'package:shridattmandir/src/shared/shared.dart' show SdmToast;

part 'analytics_state.dart';

class AnalyticsCubit extends Cubit<AnalyticsState> {
  AnalyticsCubit({
    required AnalyticsRepository analyticsRepository,
  })  : _analyticsRepository = analyticsRepository,
        super(AnalyticsInitial());

  final AnalyticsRepository _analyticsRepository;
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;

  Future<void> onTrackAnalyticsEvent(
    AnalyticsEvent event,
  ) async {
    try {
      await _analyticsRepository.track(event);
    } catch (error) {
      SdmToast.show(error.toString());
    }
  }

  Future<void> onSetUserId() async {
    try {
      await _analyticsRepository.setUserId(
        _firebaseAuth.currentUser?.uid,
      );
    } catch (error) {
      SdmToast.show(
        error.toString(),
      );
    }
  }
}
