Firebase Messaging Notification Format
======================================

For messaging UI on Firebase Console, at additional options, add:

android notification channel: `important_updates`

key: `route`

| Key   | Value                      |
|-------|----------------------------|
| route | /splash                    |
| route | /phone-registration        |
| route | /phone-otp                 |
| route | /home                      |
| route | /about-us                  |
| route | /publications              |
| route | /publication-info          |
| route | /calendar-events           |
| route | /blogs                     |
| route | /blog-view                 |
| route | /contact-us                |
| route | /videos                    |
| route | /video-player              |
| route | /sdm-web-view              |
| route | /enter-email               |
| route | /certificates              |
| route | /certificate-details       |
| route | /music-player              |
| route | /music-list                |
| route | /music-playlist            |
| route | /profile                   |
| route | /admin-home                |
| route | /blog-input                |
| route | /publication-input         |
| route | /quotes-input              |
| route | /music-input               |

key: `arguments`. Only for `/blog-view` and `/music-player`, give firebase document id as value.

| Key        | Value                      |
|------------|----------------------------|
| arguments  | document id                |
