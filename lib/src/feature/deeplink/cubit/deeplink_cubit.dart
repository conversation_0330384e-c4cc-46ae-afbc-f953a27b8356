import 'package:equatable/equatable.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_app_badger/flutter_app_badger.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/core/core.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show
        BlogsRepository,
        FlavorConfig,
        MusicRepository,
        PurchasesRepository,
        SubscriptionService,
        kEntitlementId;
import 'package:shridattmandir/src/shared/shared.dart';

part 'deeplink_state.dart';

class DeeplinkCubit extends Cubit<DeeplinkState> {
  DeeplinkCubit({
    required PurchasesRepository purchasesRepository,
    required FirebaseMessaging firebaseMessaging,
    required MusicRepository musicRepository,
    required BlogsRepository blogsRepository,
    SubscriptionService? subscriptionService,
  })  : _purchasesRepository = purchasesRepository,
        _firebaseMessaging = firebaseMessaging,
        _musicRepository = musicRepository,
        _blogsRepository = blogsRepository,
        _subscriptionService = subscriptionService,
        super(const DeeplinkState());

  final PurchasesRepository _purchasesRepository;
  final FirebaseMessaging _firebaseMessaging;
  final MusicRepository _musicRepository;
  final BlogsRepository _blogsRepository;
  final SubscriptionService? _subscriptionService;

  final String routeName = 'route';
  final String arguments = 'arguments';

  Future<void> initialize() async {
    await _requestPermission();

    if (await FlutterAppBadger.isAppBadgeSupported()) {
      await FlutterAppBadger.removeBadge();
    }

    FirebaseMessaging.onMessage.listen(
      (message) {
        safeEmit(
          state.copyWith(
            messageOnForeground: true,
            remoteMessage: () => message,
          ),
        );
      },
    );

    FirebaseMessaging.onMessageOpenedApp.listen(
      (message) async {
        safeEmit(
          state.copyWith(
            messageOnForeground: false,
            remoteMessage: () => message,
          ),
        );
        await handleNotification(message);
      },
    );

    RemoteMessage? initialMessage =
        await FirebaseMessaging.instance.getInitialMessage();

    if (initialMessage != null) {
      safeEmit(
        state.copyWith(
          messageOnForeground: false,
          remoteMessage: () => initialMessage,
        ),
      );
      await handleNotification(initialMessage);
    }
  }

  Future<void> _requestPermission() async {
    final permission = await _firebaseMessaging.requestPermission(
      sound: true,
      badge: true,
      alert: true,
    );

    if (permission.alert == AppleNotificationSetting.enabled) {
      _firebaseMessaging.setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );
    }
    debugPrint(await _firebaseMessaging.getToken());
  }

  Future<void> handleNotification(RemoteMessage message) async {
    safeEmit(
      state.copyWith(
        messageOnForeground: false,
        remoteMessage: () => message,
      ),
    );

    final data = message.data;

    if (data.isEmpty) return;

    final route = data[routeName];
    final argumentsData = data[arguments];

    if (route == null) return;

    bool hasMusicPremium = true;

    if (FlavorConfig.isProduction()) {
      if (_subscriptionService != null) {
        hasMusicPremium = _subscriptionService.hasMusicPremium;
      } else {
        // Fallback to direct repository call if service not available
        final customerInfo = await _purchasesRepository.getCustomerInfo();
        hasMusicPremium =
            customerInfo.entitlements.all[kEntitlementId]?.isActive ?? false;
      }
    }

    switch (route) {
      case SdmRouter.musicPlayer:
        if (!hasMusicPremium) {
          await navigate(
            routeName: SdmRouter.paywall,
          );
          return;
        }
        if (argumentsData == null) {
          await navigate(
            routeName: SdmRouter.musicList,
          );
          return;
        }

        try {
          final musicById =
              await _musicRepository.fetchMusicById(id: argumentsData);

          if (musicById == null) {
            await navigate(
              routeName: SdmRouter.musicList,
            );
            return;
          }

          await navigate(
            routeName: route,
            arguments: MusicPlayerArguments(
              musicList: [musicById],
            ),
          );
        } catch (e) {
          SdmToast.show(e.toString());
        }
        break;
      case SdmRouter.musicList:
        if (!hasMusicPremium) {
          await navigate(
            routeName: SdmRouter.paywall,
          );
          return;
        }
        await navigate(
          routeName: route,
        );

        break;

      case SdmRouter.blogView:
        try {
          final blogById =
              await _blogsRepository.fetchBlogById(id: argumentsData);

          await navigate(
            routeName: route,
            arguments: blogById,
          );
        } catch (e) {
          SdmToast.show(e.toString());
        }
        break;

      default:
        await navigate(
          routeName: route,
        );
    }
  }

  Future<void> navigate({
    required String routeName,
    Object? arguments,
  }) async {
    safeEmit(
      state.copyWith(
        routeName: () => routeName,
        arguments: () => arguments,
      ),
    );
  }

  void reset() {
    safeEmit(
      state.copyWith(
        routeName: () => null,
        arguments: () => null,
      ),
    );
  }
}
