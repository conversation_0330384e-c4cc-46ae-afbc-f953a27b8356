part of 'deeplink_cubit.dart';

final class DeeplinkState extends Equatable {
  const DeeplinkState({
    this.routeName,
    this.arguments,
    this.messageOnForeground = false,
    this.remoteMessage,
  });

  final String? routeName;
  final Object? arguments;
  final bool messageOnForeground;
  final RemoteMessage? remoteMessage;

  DeeplinkState copyWith({
    String? Function()? routeName,
    Object? Function()? arguments,
    bool? messageOnForeground,
    RemoteMessage Function()? remoteMessage,
  }) {
    return DeeplinkState(
      routeName: routeName != null ? routeName() : this.routeName,
      arguments: arguments != null ? arguments() : this.arguments,
      messageOnForeground: messageOnForeground ?? this.messageOnForeground,
      remoteMessage:
          remoteMessage != null ? remoteMessage() : this.remoteMessage,
    );
  }

  @override
  List<Object?> get props => [
        routeName,
        arguments,
        messageOnForeground,
        remoteMessage,
      ];
}
