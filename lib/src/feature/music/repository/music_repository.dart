import 'dart:developer';
import 'dart:typed_data';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show
        MusicModel,
        IndexEnum,
        MusicDeserializationException,
        AddMusicException,
        FetchMusicException,
        UploadMusicException;

class MusicRepository {
  const MusicRepository(
    FirebaseFirestore firestore,
    FirebaseStorage storage,
  )   : _firestore = firestore,
        _storage = storage;

  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;

  static const String _collectionName = 'music';
  static const int _limit = 10;
  static const String _filterByField = "index";
  static const String _orderBy = "title";

  static const String _musicRef = 'music';

  Future<MusicModel?> fetchMusicById({
    required String id,
  }) async {
    try {
      final DocumentSnapshot<Map<String, dynamic>> documentSnapshot =
          await _firestore.collection(_collectionName).doc(id).get();

      if (documentSnapshot.exists) {
        return MusicModel.fromJson(
          documentSnapshot.data()!,
        );
      } else {
        return null;
      }
    } on MusicDeserializationException {
      rethrow;
    } on Exception catch (error, stackTrace) {
      throw FetchMusicException(error, stackTrace);
    }
  }

  Future<
      ({
        List<MusicModel> musicModel,
        DocumentSnapshot? lastVisibleDocumentSnapshot
      })> fetchMusic({
    DocumentSnapshot? documentSnapshot,
    IndexEnum? indexEnum,
  }) async {
    try {
      Query query = _firestore
          .collection(_collectionName)
          .limit(_limit)
          .orderBy(_orderBy);

      if (documentSnapshot != null) {
        query = query.startAfterDocument(documentSnapshot);
      }
      if (indexEnum != null) {
        query = query.where(
          _filterByField,
          isEqualTo: indexEnum.name,
        );
      }

      final QuerySnapshot querySnapshot = await query.get();
      final documents = querySnapshot.docs;

      DocumentSnapshot? lastVisible;
      if (querySnapshot.size > 0) {
        lastVisible = querySnapshot.docs[querySnapshot.size - 1];
      }

      return (
        musicModel: documents.toMusic(),
        lastVisibleDocumentSnapshot: lastVisible,
      );
    } on MusicDeserializationException {
      rethrow;
    } on Exception catch (error, stackTrace) {
      throw FetchMusicException(error, stackTrace);
    }
  }

  Future<void> addMusic({
    required MusicModel musicModel,
  }) async {
    try {
      if (musicModel.documentId != null && musicModel.documentId!.isNotEmpty) {
        await updateMusic(musicModel: musicModel);
        return;
      }

      final DocumentReference document =
          await _firestore.collection(_collectionName).add(
                musicModel.toJson(),
              );
      await document.update({
        'documentId': document.id,
        'uploadDate': FieldValue.serverTimestamp(),
      });
    } on Exception catch (error, stackTrace) {
      throw AddMusicException(error, stackTrace);
    }
  }

  Future<void> updateMusic({
    required MusicModel musicModel,
  }) async {
    try {
      await _firestore
          .collection(_collectionName)
          .doc(musicModel.documentId)
          .update(
            musicModel.toJson(),
          );
    } on Exception catch (error, stackTrace) {
      throw AddMusicException(error, stackTrace);
    }
  }

  //upload music to firebase storage
  Future<String?> uploadMusicToFirebaseStorage({
    required Uint8List bytes,
    required String fileName,
    required String extension,
  }) async {
    String? url;

    try {
      final storageRef = _storage.ref();

      final musicRef = storageRef.child(_musicRef);

      final uploadTask = musicRef.child(fileName).putData(
            bytes,
            SettableMetadata(
              contentType: 'audio/$extension',
            ),
          );

      await uploadTask.whenComplete(() {
        log('File Uploaded');
      });

      url = await musicRef.child(fileName).getDownloadURL();
    } on Exception catch (error, stackTrace) {
      throw UploadMusicException(error, stackTrace);
    }

    return url;
  }
}

extension on List<QueryDocumentSnapshot> {
  List<MusicModel> toMusic() {
    final music = <MusicModel>[];
    for (final document in this) {
      final data = document.data() as Map<String, dynamic>?;
      if (data != null) {
        try {
          music.add(MusicModel.fromJson(data));
        } catch (error, stackTrace) {
          throw MusicDeserializationException(error, stackTrace);
        }
      }
    }
    return music;
  }
}
