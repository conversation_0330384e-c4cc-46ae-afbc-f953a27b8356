part of 'music_list_cubit.dart';

final class MusicListState extends Equatable {
  const MusicListState({
    this.musicList,
    this.lastVisibleDocumentSnapshot,
    this.loading = false,
    this.loadMore = false,
    this.hasReachedMax = false,
    this.indexEnum,
    this.favoriteMusicDocumentIds,
    this.favoritesMusicList = const [],
    this.user,
  });

  final List<MusicModel>? musicList;
  final DocumentSnapshot? lastVisibleDocumentSnapshot;
  final bool loading;
  final bool loadMore;
  final bool hasReachedMax;
  final IndexEnum? indexEnum;
  final List<String>? favoriteMusicDocumentIds;
  final List<MusicModel?> favoritesMusicList;
  final UserModel? user;

  MusicListState copyWith({
    List<MusicModel>? musicList,
    DocumentSnapshot? Function()? lastVisibleDocumentSnapshot,
    bool? loading,
    bool? loadMore,
    bool? hasReachedMax,
    IndexEnum? Function()? indexEnum,
    List<String>? favoriteMusicDocumentIds,
    List<MusicModel?>? favoritesMusicList,
    UserModel? user,
  }) {
    return MusicListState(
      musicList: musicList ?? this.musicList,
      lastVisibleDocumentSnapshot: lastVisibleDocumentSnapshot != null
          ? lastVisibleDocumentSnapshot()
          : this.lastVisibleDocumentSnapshot,
      loading: loading ?? this.loading,
      loadMore: loadMore ?? this.loadMore,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      indexEnum: indexEnum != null ? indexEnum() : this.indexEnum,
      favoriteMusicDocumentIds:
          favoriteMusicDocumentIds ?? this.favoriteMusicDocumentIds,
      favoritesMusicList: favoritesMusicList ?? this.favoritesMusicList,
      user: user ?? this.user,
    );
  }

  @override
  List<Object?> get props => [
        musicList,
        lastVisibleDocumentSnapshot,
        loading,
        loadMore,
        hasReachedMax,
        indexEnum,
        favoriteMusicDocumentIds,
        favoritesMusicList,
        user,
      ];
}
