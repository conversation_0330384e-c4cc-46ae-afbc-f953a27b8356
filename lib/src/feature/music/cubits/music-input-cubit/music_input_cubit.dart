import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/core/core.dart' show CubitExt, SdmRouter;
import 'package:shridattmandir/src/feature/feature.dart'
    show MusicRepository, MusicModel, IndexEnum;
import 'package:shridattmandir/src/shared/shared.dart' show SdmToast;

part 'music_input_state.dart';

class MusicInputCubit extends Cubit<MusicInputState> {
  MusicInputCubit() : super(const MusicInputState());

  final TextEditingController lyricistController = TextEditingController();
  final TextEditingController titleController = TextEditingController();
  final TextEditingController lyricsController = TextEditingController();
  final TextEditingController artUrlController = TextEditingController();

  final MusicRepository _musicRepository = MusicRepository(
    FirebaseFirestore.instance,
    FirebaseStorage.instance,
  );

  Future<void> fetchMusicById(String? id) async {
    if (id == null || id.isEmpty) {
      return;
    }

    safeEmit(
      state.copyWith(
        loading: true,
      ),
    );

    try {
      final musicModel = await _musicRepository.fetchMusicById(id: id);

      lyricistController.text = musicModel?.lyricist ?? '';
      titleController.text = musicModel?.title ?? '';
      lyricsController.text = musicModel?.lyrics ?? '';
      artUrlController.text = musicModel?.artUrl ?? '';

      safeEmit(
        state.copyWith(
          loading: false,
          category: musicModel?.category,
          referenceBook: musicModel?.referenceBook,
          indexEnum: musicModel?.indexEnum,
        ),
      );
    } catch (e) {
      SdmToast.show(
        'Failed to fetch music.',
      );
    }
  }

  void setIndex(IndexEnum indexEnum) {
    safeEmit(
      state.copyWith(
        indexEnum: indexEnum,
      ),
    );
  }

  void setCategory(String category) {
    safeEmit(
      state.copyWith(
        category: category,
      ),
    );
    log('type: $category');
  }

  void setReferenceBook(String referenceBook) {
    safeEmit(
      state.copyWith(
        referenceBook: referenceBook,
      ),
    );
    log('referenceBook: $referenceBook');
  }

  void clearControllers() {
    lyricistController.clear();
    titleController.clear();
    lyricsController.clear();
    artUrlController.clear();
  }

  Future<void> updateMusic({required MusicModel musicModel}) async {
    if (lyricistController.text.isEmpty ||
        titleController.text.isEmpty ||
        lyricsController.text.isEmpty ||
        state.indexEnum == null) {
      SdmToast.show(
        'Lyricist, Title, Lyrics and Index are required fields.',
      );
      return;
    }
    safeEmit(
      state.copyWith(
        loading: true,
      ),
    );

    await uploadToFirebaseStorage();

    try {
      await _musicRepository.addMusic(
        musicModel: musicModel.copyWith(
          lyricist: lyricistController.text,
          title: titleController.text,
          lyrics: lyricsController.text,
          artUrl: artUrlController.text,
          category: state.category,
          referenceBook: state.referenceBook,
          indexEnum: state.indexEnum,
          audioUrl: state.audioUrl,
        ),
      );

      SdmToast.show(
        'Music added successfully.',
      );

      clearControllers();
      safeEmit(const MusicInputState());
      SdmRouter.navigatorKey.currentState?.pop();
    } catch (e) {
      SdmToast.show(
        'Failed to add music.',
      );
    }

    safeEmit(
      state.copyWith(
        loading: false,
      ),
    );
  }

  Future<void> addMusic() async {
    if (lyricistController.text.isEmpty ||
        titleController.text.isEmpty ||
        lyricsController.text.isEmpty ||
        state.indexEnum == null) {
      SdmToast.show(
        'Lyricist, Title, Lyrics and Index are required fields.',
      );
      return;
    }

    safeEmit(
      state.copyWith(
        loading: true,
      ),
    );

    await uploadToFirebaseStorage();

    if (state.audioUrl == null) {
      SdmToast.show(
        'Audio file not uploaded.',
      );
      safeEmit(
        state.copyWith(
          loading: false,
        ),
      );
      return;
    }

    try {
      await _musicRepository.addMusic(
        musicModel: MusicModel(
          lyricist: lyricistController.text,
          title: titleController.text,
          lyrics: lyricsController.text,
          artUrl: artUrlController.text,
          audioUrl: state.audioUrl,
          category: state.category,
          referenceBook: state.referenceBook,
          indexEnum: state.indexEnum,
        ),
      );

      SdmToast.show(
        'Music added successfully.',
      );

      clearControllers();
      safeEmit(const MusicInputState());
    } catch (e) {
      SdmToast.show(
        'Failed to add music.',
      );
    }
  }

  Future<void> pickAudioToUpload() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      withData: true,
      allowedExtensions: [
        'mp3',
        'wav',
        'aac',
        'm4a',
        'ogg',
        'flac',
        'wma',
      ],
    );

    if (result != null) {
      safeEmit(
        state.copyWith(
          filePickerResult: result,
        ),
      );
    } else {
      SdmToast.show(
        'No file selected.',
      );
    }
  }

  Future<void> uploadToFirebaseStorage() async {
    if (state.filePickerResult == null) {
      SdmToast.show(
        'Please select a file to upload.',
      );
      return;
    }

    safeEmit(
      state.copyWith(
        loading: true,
      ),
    );

    try {
      final FilePickerResult filePickerResult = state.filePickerResult!;

      final String? url = await _musicRepository.uploadMusicToFirebaseStorage(
        bytes: filePickerResult.files.single.bytes!,
        fileName: filePickerResult.files.single.name,
        extension: filePickerResult.files.single.extension ?? 'mp3',
      );

      safeEmit(
        state.copyWith(
          audioUrl: url,
        ),
      );

      SdmToast.show(
        'File uploaded successfully.',
      );
    } catch (e) {
      SdmToast.show(
        'Failed to upload file.',
      );
    }

    safeEmit(
      state.copyWith(
        loading: false,
      ),
    );
  }

  @override
  Future<void> close() {
    lyricistController.dispose();
    titleController.dispose();
    lyricsController.dispose();
    artUrlController.dispose();
    return super.close();
  }
}
