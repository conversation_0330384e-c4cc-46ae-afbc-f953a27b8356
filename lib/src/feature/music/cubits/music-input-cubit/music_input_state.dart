part of 'music_input_cubit.dart';

final class MusicInputState extends Equatable {
  const MusicInputState({
    this.filePickerResult,
    this.loading = false,
    this.audioUrl,
    this.category,
    this.indexEnum,
    this.referenceBook,
  });

  final bool loading;
  final FilePickerResult? filePickerResult;
  final String? audioUrl;
  final String? category;
  final IndexEnum? indexEnum;
  final String? referenceBook;

  MusicInputState copyWith({
    FilePickerResult? filePickerResult,
    bool? loading,
    String? audioUrl,
    String? category,
    IndexEnum? indexEnum,
    String? referenceBook,
  }) {
    return MusicInputState(
      filePickerResult: filePickerResult ?? this.filePickerResult,
      loading: loading ?? this.loading,
      audioUrl: audioUrl ?? this.audioUrl,
      category: category ?? this.category,
      indexEnum: indexEnum ?? this.indexEnum,
      referenceBook: referenceBook ?? this.referenceBook,
    );
  }

  @override
  List<Object?> get props => [
        filePickerResult,
        loading,
        audioUrl,
        category,
        indexEnum,
        referenceBook,
      ];
}
