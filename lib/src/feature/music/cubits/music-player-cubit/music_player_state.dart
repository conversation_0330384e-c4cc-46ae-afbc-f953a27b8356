part of 'music_player_cubit.dart';

final class MusicPlayerState extends Equatable {
  const MusicPlayerState({
    this.playerButtonState = PlayerButtonState.paused,
    this.positionDuration,
    this.totalDuration,
    this.bufferedDuration,
    this.playlistChildren,
    this.currentMusic,
    this.playlistCurrentIndex = -1,
    this.lastVisibleDocumentSnapshot,
    this.indexEnum,
    this.loopMode = LoopModeEnum.once,
    this.autoPlayLimit = 10,
    this.playbackSpeed = 1.0,
    this.isShuffleEnabled = false,
    this.queueSize = 0,
    this.initialStartIndex = -1,
  });

  final PlayerButtonState playerButtonState;
  final Duration? positionDuration;
  final Duration? totalDuration;
  final Duration? bufferedDuration;
  final List<MusicModel>? playlistChildren;
  final MusicModel? currentMusic;
  final int playlistCurrentIndex;
  final DocumentSnapshot? lastVisibleDocumentSnapshot;
  final IndexEnum? indexEnum;
  final LoopModeEnum loopMode;
  final int autoPlayLimit;
  final double playbackSpeed;
  final bool isShuffleEnabled;
  final int queueSize;
  final int initialStartIndex;

  //copyWith
  MusicPlayerState copyWith({
    PlayerButtonState? playerButtonState,
    Duration? positionDuration,
    Duration? totalDuration,
    Duration? bufferedDuration,
    List<MusicModel>? playlistChildren,
    MusicModel? Function()? currentMusic,
    int? playlistCurrentIndex,
    DocumentSnapshot? lastVisibleDocumentSnapshot,
    IndexEnum? indexEnum,
    LoopModeEnum? loopMode,
    int? autoPlayLimit,
    double? playbackSpeed,
    bool? isShuffleEnabled,
    int? queueSize,
    int? initialStartIndex,
  }) {
    return MusicPlayerState(
      playerButtonState: playerButtonState ?? this.playerButtonState,
      positionDuration: positionDuration ?? this.positionDuration,
      totalDuration: totalDuration ?? this.totalDuration,
      bufferedDuration: bufferedDuration ?? this.bufferedDuration,
      playlistChildren: playlistChildren ?? this.playlistChildren,
      currentMusic: currentMusic != null ? currentMusic() : this.currentMusic,
      playlistCurrentIndex: playlistCurrentIndex ?? this.playlistCurrentIndex,
      lastVisibleDocumentSnapshot:
          lastVisibleDocumentSnapshot ?? this.lastVisibleDocumentSnapshot,
      indexEnum: indexEnum ?? this.indexEnum,
      loopMode: loopMode ?? this.loopMode,
      autoPlayLimit: autoPlayLimit ?? this.autoPlayLimit,
      playbackSpeed: playbackSpeed ?? this.playbackSpeed,
      isShuffleEnabled: isShuffleEnabled ?? this.isShuffleEnabled,
      queueSize: queueSize ?? this.queueSize,
      initialStartIndex: initialStartIndex ?? this.initialStartIndex,
    );
  }

  @override
  List<Object?> get props => [
        playerButtonState,
        positionDuration,
        totalDuration,
        bufferedDuration,
        playlistChildren,
        currentMusic,
        playlistCurrentIndex,
        lastVisibleDocumentSnapshot,
        indexEnum,
        loopMode,
        autoPlayLimit,
        playbackSpeed,
        isShuffleEnabled,
        queueSize,
        initialStartIndex,
      ];
}
