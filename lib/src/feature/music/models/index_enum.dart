enum IndexEnum {
  a('अ'),
  aa('आ'),
  e('इ'),
  ee('ई'),
  u('उ'),
  uu('ऊ'),
  ae('ए'),
  ai('ऐ'),
  o('ओ'),
  au('औ'),
  am('अं'),
  k('क'),
  kh('ख'),
  g('ग'),
  gh('घ'),
  ch('च'),
  chh('छ'),
  j('ज'),
  jh('झ'),
  tt('ट'),
  tth('ठ'),
  dd('ड'),
  dhh('ढ'),
  t('त'),
  th('थ'),
  d('द'),
  dh('ध'),
  n('न'),
  p('प'),
  ph('फ'),
  b('ब'),
  bh('भ'),
  m('म'),
  y('य'),
  r('र'),
  l('ल'),
  v('व'),
  sh('श'),
  s('स'),
  h('ह'),
  ksh('क्ष'),
  dny('ज्ञ'),
  ru('ऋ'),
  hru('हृ'),
  shr('श्र'),
  tr('त्र'),
  om('ॐ');

  final String devnagriEnumValue;
  const IndexEnum(this.devnagriEnumValue);
}

IndexEnum? indexEnumFromJson(String? str) {
  if (str == null || str.isEmpty) return null;
  return IndexEnum.values.firstWhere(
    (element) => element.name == str,
  );
}
