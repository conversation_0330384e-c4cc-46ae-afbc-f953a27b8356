import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show IndexEnum, indexEnumFromJson;

MusicModel musicModelFromJson(String str) =>
    MusicModel.fromJson(json.decode(str));

String musicModelToJson(MusicModel data) => json.encode(data.toJson());

class MusicModel {
  final String? documentId;
  final String? audioUrl;
  final Timestamp? uploadDate;
  final String? lyricist;
  final String? category;
  final IndexEnum? indexEnum;
  final String? title;
  final String? lyrics;
  final String? referenceBook;
  final String? artUrl;

  MusicModel({
    this.documentId,
    this.audioUrl,
    this.uploadDate,
    this.lyricist,
    this.category,
    this.indexEnum,
    this.title,
    this.lyrics,
    this.referenceBook,
    this.artUrl,
  });

  factory MusicModel.fromJson(Map<String, dynamic> json) => MusicModel(
        documentId: json["documentId"],
        audioUrl: json["audioUrl"],
        uploadDate: json["uploadDate"],
        lyricist: json["lyricist"],
        category: json["category"],
        indexEnum:
            json["index"] == null ? null : indexEnumFromJ<PERSON>(json["index"]),
        title: json["title"],
        lyrics: json["lyrics"],
        referenceBook: json["referenceBook"],
        artUrl: json["artUrl"],
      );

  //copyWith
  MusicModel copyWith({
    String? documentId,
    String? audioUrl,
    Timestamp? uploadDate,
    String? lyricist,
    String? category,
    IndexEnum? indexEnum,
    String? title,
    String? lyrics,
    String? referenceBook,
    String? artUrl,
  }) {
    return MusicModel(
      documentId: documentId ?? this.documentId,
      audioUrl: audioUrl ?? this.audioUrl,
      uploadDate: uploadDate ?? this.uploadDate,
      lyricist: lyricist ?? this.lyricist,
      category: category ?? this.category,
      indexEnum: indexEnum ?? this.indexEnum,
      title: title ?? this.title,
      lyrics: lyrics ?? this.lyrics,
      referenceBook: referenceBook ?? this.referenceBook,
      artUrl: artUrl ?? this.artUrl,
    );
  }

  Map<String, dynamic> toJson() => {
        "documentId": documentId,
        "audioUrl": audioUrl,
        "uploadDate": uploadDate,
        "lyricist": lyricist,
        "category": category,
        "index": indexEnum?.name,
        "title": title,
        "lyrics": lyrics,
        "referenceBook": referenceBook,
        "artUrl": artUrl,
      };
}
