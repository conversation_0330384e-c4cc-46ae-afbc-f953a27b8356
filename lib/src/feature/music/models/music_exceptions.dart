abstract class MusicException implements Exception {
  const MusicException(this.error, this.stackTrace);

  final Object error;
  final StackTrace stackTrace;
}

class FetchMusicException extends MusicException {
  const FetchMusicException(super.error, super.stackTrace);
}

class MusicDeserializationException extends MusicException {
  const MusicDeserializationException(super.error, super.stackTrace);
}

class AddMusicException extends MusicException {
  const AddMusicException(super.error, super.stackTrace);
}

class UploadMusicException extends MusicException {
  const UploadMusicException(super.error, super.stackTrace);
}
