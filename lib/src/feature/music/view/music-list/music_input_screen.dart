import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;
import 'package:shridattmandir/src/feature/feature.dart'
    show
        MusicInputCubit,
        MusicInputState,
        RemoteConfigCubit,
        MusicModel,
        IndexEnum;
import 'package:shridattmandir/src/shared/shared.dart'
    show SdmTextField, SdmDropDownButton, SdmPrimaryCta;

class MusicInputScreen extends StatelessWidget {
  const MusicInputScreen({super.key, this.args});

  final MusicModel? args;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Music Input',
          style: TextStyle(
            color: SdmPalette.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        iconTheme: const IconThemeData(
          color: SdmPalette.black,
        ),
      ),
      body: BlocProvider(
        create: (context) =>
            MusicInputCubit()..fetchMusicById(args?.documentId),
        child: BlocBuilder<MusicInputCubit, MusicInputState>(
          builder: (context, state) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 30.0,
                ),
                child: ListView(
                  shrinkWrap: true,
                  children: [
                    GestureDetector(
                      onTap: context.read<MusicInputCubit>().pickAudioToUpload,
                      child: Container(
                        height: 100,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            color: SdmPalette.textColorGrey2,
                            width: 0.5,
                            style: BorderStyle.solid,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            args?.title ??
                                state.filePickerResult?.files.single.name ??
                                'Tap to select audio file',
                            style: const TextStyle(
                              color: SdmPalette.black,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    SdmTextField(
                      hintText: 'Enter Lyricist Name',
                      controller:
                          context.read<MusicInputCubit>().lyricistController,
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    SdmTextField(
                      hintText: 'Enter Title',
                      controller:
                          context.read<MusicInputCubit>().titleController,
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    SdmTextField(
                      hintText: 'Enter Lyrics',
                      maxLines: 8,
                      controller:
                          context.read<MusicInputCubit>().lyricsController,
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    SdmTextField(
                      hintText: 'Enter art url (optional)',
                      controller:
                          context.read<MusicInputCubit>().artUrlController,
                    ),
                    const SizedBox(
                      height: 30,
                    ),
                    SdmDropDownButton(
                      value: state.indexEnum,
                      items: IndexEnum.values
                          .map(
                            (e) => DropdownMenuItem(
                              value: e,
                              child: Text(
                                e.devnagriEnumValue,
                              ),
                            ),
                          )
                          .toList(),
                      hint: const Text('Select Index'),
                      onChanged: (val) {
                        context
                            .read<MusicInputCubit>()
                            .setIndex(val as IndexEnum);
                      },
                    ),
                    const SizedBox(
                      height: 30,
                    ),
                    SdmDropDownButton(
                      value: state.category,
                      items: context
                          .read<RemoteConfigCubit>()
                          .state
                          .categories
                          .map(
                            (e) => DropdownMenuItem(
                              value: e,
                              child: Text(
                                e,
                              ),
                            ),
                          )
                          .toList(),
                      hint: const Text('Select Category'),
                      onChanged: (val) {
                        context
                            .read<MusicInputCubit>()
                            .setCategory(val as String);
                      },
                    ),
                    const SizedBox(
                      height: 30,
                    ),
                    SdmDropDownButton(
                      value: state.referenceBook,
                      items: context
                          .read<RemoteConfigCubit>()
                          .state
                          .referenceBooks
                          .map(
                            (e) => DropdownMenuItem(
                              value: e,
                              child: Text(
                                e,
                              ),
                            ),
                          )
                          .toList(),
                      hint: const Text('Select Reference Book'),
                      onChanged: (val) {
                        context
                            .read<MusicInputCubit>()
                            .setReferenceBook(val as String);
                      },
                    ),
                    const SizedBox(
                      height: 30,
                    ),
                    Center(
                      child: SdmPrimaryCta(
                        onPressed: () async {
                          if (args != null) {
                            await context
                                .read<MusicInputCubit>()
                                .updateMusic(musicModel: args!);
                          } else {
                            await context.read<MusicInputCubit>().addMusic();
                          }
                        },
                        child: state.loading
                            ? const CircularProgressIndicator.adaptive()
                            : Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 30.0,
                                ),
                                child: Text(
                                  args != null
                                      ? 'Update Music'
                                      : 'Upload Music',
                                  style: const TextStyle(
                                    color: SdmPalette.white,
                                  ),
                                ),
                              ),
                      ),
                    ),
                    const SizedBox(
                      height: 30,
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
