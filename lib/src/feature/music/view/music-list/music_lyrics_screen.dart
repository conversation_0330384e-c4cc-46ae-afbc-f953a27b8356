import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;
import 'package:shridattmandir/src/feature/music/cubits/music-player-cubit/music_player_cubit.dart';

class MusicLyricsScreen extends StatelessWidget {
  const MusicLyricsScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MusicPlayerCubit, MusicPlayerState>(
      builder: (context, state) {
        final text = state.currentMusic?.lyrics?.replaceAll(
          '\\n',
          '\n',
        );
        return Container(
          height: MediaQuery.of(context).size.height,
          padding: const EdgeInsets.only(
            left: 16,
            right: 16,
          ).w,
          decoration: BoxDecoration(
            color: SdmPalette.black.withValues(
              alpha: 0.8,
            ),
          ),
          child: <PERSON>umn(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 40.h,
              ),
              Align(
                alignment: Alignment.topRight,
                child: IconButton(
                  iconSize: 30.w,
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: Icon(
                    Icons.close,
                    color: SdmPalette.white,
                    size: 30.w,
                  ),
                ),
              ),
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.only(
                    top: 0,
                    bottom: 24,
                  ),
                  child: SelectableText(
                    text ?? '',
                    style: TextStyle(
                      color: SdmPalette.white,
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
