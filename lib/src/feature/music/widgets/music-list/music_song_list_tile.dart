import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;
import 'package:shridattmandir/src/feature/feature.dart';

class MusicSongListTile extends StatelessWidget {
  const MusicSongListTile({
    super.key,
    required this.musicItem,
    required this.index,
    this.isPlaying = false,
    this.onTap,
  });

  final MusicModel musicItem;
  final int index;
  final bool isPlaying;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MusicListCubit, MusicListState>(
      builder: (context, state) {
        return Dismissible(
          direction: DismissDirection.endToStart,
          key: Key(musicItem.documentId ?? ''),
          confirmDismiss: (direction) async {
            context
                .read<MusicListCubit>()
                .addRemoveFavorites(musicItem: musicItem);

            return false;
          },
          background: Container(
            color: SdmPalette.lightRed,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  state.favoriteMusicDocumentIds
                              ?.contains(musicItem.documentId) ==
                          true
                      ? 'Remove from favorites'
                      : 'Add to favorites',
                  style: TextStyle(
                    color: SdmPalette.white,
                    fontSize: 13.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(
                  width: 16.w,
                ),
                Padding(
                  padding: const EdgeInsets.only(
                    right: 16,
                  ).w,
                  child: const Icon(
                    Icons.favorite,
                    color: SdmPalette.white,
                  ),
                ),
              ],
            ),
          ),
          child: ListTile(
              horizontalTitleGap: 16.w,
              minVerticalPadding: 16.h,
              titleAlignment: ListTileTitleAlignment.center,
              leading: MusicListTileLeading(
                isPlaying: isPlaying,
              ),
              trailing: MusicListTileTrailing(
                musicItem: musicItem,
                index: index,
              ),
              title: MusicListTileTitle(
                musicItem: musicItem,
                isPlaying: isPlaying,
              ),
              subtitle: Text(
                musicItem.lyricist ?? '',
                style: TextStyle(
                  color: SdmPalette.textColorGrey2,
                  fontSize: 13.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              onTap: onTap),
        );
      },
    );
  }
}
