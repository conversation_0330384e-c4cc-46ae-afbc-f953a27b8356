import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;
import 'package:shridattmandir/src/feature/music/music.dart';

class MusicListTileTitle extends StatelessWidget {
  const MusicListTileTitle({
    super.key,
    required this.musicItem,
    this.isPlaying = false,
  });

  final MusicModel musicItem;
  final bool isPlaying;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MusicListCubit, MusicListState>(
      builder: (context, state) {
        return Text(
          musicItem.title ?? '',
          style: TextStyle(
            color: (isPlaying) ? SdmPalette.primary : SdmPalette.black,
            fontSize: 15.sp,
            fontWeight: FontWeight.w700,
          ),
        );
      },
    );
  }
}
