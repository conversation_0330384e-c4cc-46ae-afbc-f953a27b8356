import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;
import 'package:shridattmandir/src/feature/feature.dart'
    show MusicListCubit, MusicListState;

class MusicClearFilterButton extends StatelessWidget {
  const MusicClearFilterButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MusicListCubit, MusicListState>(
      builder: (context, state) {
        return TextButton(
          onPressed: () async {
            context.read<MusicListCubit>()
              ..setIndexEnumAndResetList(null)
              ..fetchMusic();
          },
          style: TextButton.styleFrom(
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            minimumSize: Size.zero,
            foregroundColor: SdmPalette.primary.withValues(
              alpha: 0.2,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            side: BorderSide(
              color: SdmPalette.primary.withValues(
                alpha: 0.2,
              ),
            ),
            padding: const EdgeInsets.symmetric(
              horizontal: 8,
              vertical: 4,
            ),
          ),
          child: Text(
            'Clear - ${state.indexEnum?.devnagriEnumValue ?? ''}',
            style: const TextStyle(
              fontSize: 12,
              color: SdmPalette.primary,
              fontWeight: FontWeight.w700,
            ),
          ),
        );
      },
    );
  }
}
