import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;
import 'package:shridattmandir/src/feature/feature.dart';

class MusicListTileTrailing extends StatelessWidget {
  const MusicListTileTrailing(
      {super.key, required this.musicItem, required this.index});

  final MusicModel musicItem;
  final int index;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MusicListCubit, MusicListState>(
      builder: (context, state) {
        final isFavorite =
            state.favoriteMusicDocumentIds?.contains(musicItem.documentId) ==
                true;

        if (!isFavorite) {
          return const SizedBox.shrink();
        }
        return const Icon(
          Icons.favorite,
          color: SdmPalette.primary,
        );
      },
    );
  }
}
