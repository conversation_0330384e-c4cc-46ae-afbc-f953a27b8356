import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show Assets, SdmPalette;
import 'package:shridattmandir/src/feature/feature.dart'
    show MusicListCubit, MusicListState;

class MusicListTileLeading extends StatelessWidget {
  const MusicListTileLeading({
    super.key,
    this.isPlaying = false,
  });

  final bool isPlaying;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MusicListCubit, MusicListState>(
      builder: (context, state) {
        if (isPlaying) {
          return Padding(
            padding: const EdgeInsets.only(left: 8.0),
            child: Assets.sdmImages.musicBar.image(
              height: 20.w,
              width: 20.w,
              colorBlendMode: BlendMode.srcIn,
              color: SdmPalette.primary,
            ),
          );
        }

        return Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Assets.sdmIcons.music.svg(
            height: 20.w,
            width: 20.w,
          ),
        );
      },
    );
  }
}
