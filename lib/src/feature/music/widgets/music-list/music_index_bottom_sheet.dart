import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;
import 'package:shridattmandir/src/feature/feature.dart'
    show IndexEnum, MusicListCubit, MusicListState;

class MusicIndexBottomSheet extends StatelessWidget {
  const MusicIndexBottomSheet({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MusicListCubit, MusicListState>(
      builder: (context, state) {
        return DraggableScrollableSheet(
          expand: false,
          snap: true,
          minChildSize: 0.4,
          initialChildSize: 0.6,
          maxChildSize: 0.75,
          builder: (context, scrollController) {
            return GridView.count(
              controller: scrollController,
              crossAxisCount: 4,
              children: List.generate(
                IndexEnum.values.length,
                (index) {
                  return Center(
                    child: TextButton(
                      onPressed: state.loading
                          ? null
                          : () async {
                              final musicCubit = context.read<MusicListCubit>();
                              if (state.indexEnum == IndexEnum.values[index]) {
                                Navigator.pop(context);
                                return;
                              }

                              musicCubit.setIndexEnumAndResetList(
                                IndexEnum.values[index],
                              );

                              await musicCubit.fetchMusic();

                              if (context.mounted) {
                                Navigator.pop(context);
                              }
                            },
                      style: TextButton.styleFrom(
                        foregroundColor: SdmPalette.primary.withValues(
                          alpha: 0.2,
                        ),
                        backgroundColor:
                            state.indexEnum == IndexEnum.values[index]
                                ? SdmPalette.primary.withValues(
                                    alpha: 0.2,
                                  )
                                : null,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        side: BorderSide(
                          color: SdmPalette.primary.withValues(
                            alpha: 0.2,
                          ),
                        ),
                        padding: EdgeInsets.zero,
                      ),
                      child: Text(
                        IndexEnum.values[index].devnagriEnumValue,
                        style: TextStyle(
                          fontSize: 24.sp,
                          color: SdmPalette.primary,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ),
                  );
                },
              ),
            );
          },
        );
      },
    );
  }
}
