import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;
import 'package:shridattmandir/src/feature/feature.dart'
    show MusicPlayerCubit, MusicPlayerState, PlayerButtonState;

class PlayerButton extends StatelessWidget {
  const PlayerButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MusicPlayerCubit, MusicPlayerState>(
      builder: (context, state) {
        switch (state.playerButtonState) {
          case PlayerButtonState.playing:
            return IconButton(
              iconSize: 60.w,
              padding: EdgeInsets.zero,
              onPressed: () {
                context.read<MusicPlayerCubit>().pause();
              },
              icon: const Icon(
                Icons.pause_circle_filled,
                color: SdmPalette.primary,
              ),
            );
          case PlayerButtonState.paused:
            return IconButton(
              iconSize: 60.w,
              padding: EdgeInsets.zero,
              onPressed: () {
                context.read<MusicPlayerCubit>().play();
              },
              icon: const Icon(
                Icons.play_circle_fill,
                color: SdmPalette.primary,
              ),
            );

          case PlayerButtonState.loading:
            return SizedBox(
              height: 60.w,
              width: 60.w,
              child: const CircularProgressIndicator.adaptive(),
            );
        }
      },
    );
  }
}
