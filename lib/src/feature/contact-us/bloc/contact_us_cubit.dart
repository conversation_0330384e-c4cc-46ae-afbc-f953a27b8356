import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_email_sender/flutter_email_sender.dart';
import 'package:shridattmandir/src/shared/shared.dart' show SdmToast;

part 'contact_us_state.dart';

class ContactUsCubit extends Cubit<ContactUsState> {
  ContactUsCubit() : super(const ContactUsState());

  final TextEditingController nameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController messageController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();

  Future<void> sendEmail() async {
    if (nameController.text.isEmpty ||
        emailController.text.isEmpty ||
        messageController.text.isEmpty ||
        phoneController.text.isEmpty) {
      SdmToast.show(
        'Please fill all the fields',
      );
      return;
    }

    final Email email = Email(
      body:
          '${messageController.text}   \n Here are my details ${emailController.text} and ${phoneController.text}',
      subject: 'More Info Request from ${nameController.text}',
      recipients: ['<EMAIL>'],
      isHTML: true,
    );

    try {
      await FlutterEmailSender.send(
        email,
      );
    } catch (error) {
      SdmToast.show(
        error.toString(),
      );
    }
  }
}
