import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/shared/shared.dart' show SdmLaunchUrl;

class SocialLinks extends StatelessWidget {
  const SocialLinks({
    super.key,
    required this.text,
    required this.url,
    required this.icon,
    this.textSize,
  });

  final String text;
  final String url;
  final Widget icon;
  final double? textSize;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        SdmLaunchUrl.sdmLaunchUrl(
          url,
        );
      },
      child: Row(
        children: <Widget>[
          icon,
          const SizedBox(
            width: 10,
          ),
          Flexible(
            child: Text(
              text,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: textSize ?? 18.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
