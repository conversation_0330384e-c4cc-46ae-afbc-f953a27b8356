import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show
        BlogModel,
        BlogsDeserializationException,
        FetchBlogsException,
        AddBlogException;

class BlogsRepository {
  const BlogsRepository(
    FirebaseFirestore firestore,
  ) : _firestore = firestore;

  final FirebaseFirestore _firestore;

  static const String _collectionName = 'blogs';
  static const String _orderBy = 'timestamp';
  static const int _limit = 10;

  Future<
      ({
        List<BlogModel> blogModel,
        DocumentSnapshot? lastVisibleDocumentSnapshot
      })> fetchBlogs({DocumentSnapshot? documentSnapshot}) async {
    try {
      Query query = _firestore
          .collection(_collectionName)
          .orderBy(_orderBy, descending: true)
          .limit(_limit);

      if (documentSnapshot != null) {
        query = query.startAfterDocument(documentSnapshot);
      }

      final QuerySnapshot querySnapshot = await query.get();
      final documents = querySnapshot.docs;
      DocumentSnapshot? lastVisible;
      if (querySnapshot.size > 0) {
        lastVisible = querySnapshot.docs[querySnapshot.size - 1];
      }

      return (
        blogModel: documents.toBlog(),
        lastVisibleDocumentSnapshot: lastVisible,
      );
    } on BlogsDeserializationException {
      rethrow;
    } on Exception catch (error, stackTrace) {
      throw FetchBlogsException(error, stackTrace);
    }
  }

  //fetch blog by id
  Future<BlogModel?> fetchBlogById({
    required String id,
  }) async {
    try {
      final DocumentSnapshot<Map<String, dynamic>> documentSnapshot =
          await _firestore.collection(_collectionName).doc(id).get();

      if (documentSnapshot.exists) {
        return BlogModel.fromJson(
          documentSnapshot.data()!,
        );
      } else {
        return null;
      }
    } on BlogsDeserializationException {
      rethrow;
    } on Exception catch (error, stackTrace) {
      throw FetchBlogsException(error, stackTrace);
    }
  }

  Future<void> addBlog(BlogModel blog) async {
    try {
      await _firestore.collection(_collectionName).add(
            blog.toJson(),
          );
    } on Exception catch (error, stackTrace) {
      throw AddBlogException(error, stackTrace);
    }
  }
}

extension on List<QueryDocumentSnapshot> {
  List<BlogModel> toBlog() {
    final blogs = <BlogModel>[];

    for (final document in this) {
      final data = document.data() as Map<String, dynamic>?;
      if (data != null) {
        try {
          blogs.add(BlogModel.fromJson(data));
        } catch (error, stackTrace) {
          throw BlogsDeserializationException(error, stackTrace);
        }
      }
    }
    return blogs;
  }
}
