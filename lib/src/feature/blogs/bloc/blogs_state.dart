part of 'blogs_cubit.dart';

final class BlogsState extends Equatable {
  const BlogsState({
    this.blogs,
    this.loadMore = false,
    this.lastVisible,
    this.hasReachedMax = false,
    this.zoom = 1.0,
  });

  final List<BlogModel>? blogs;
  final bool loadMore;
  final bool hasReachedMax;
  final DocumentSnapshot? lastVisible;
  final double zoom;

  @override
  List<Object?> get props => [
        blogs,
        lastVisible,
        loadMore,
        hasReachedMax,
        zoom,
      ];

  BlogsState copyWith({
    List<BlogModel>? blogs,
    DocumentSnapshot? lastVisible,
    bool? loadMore,
    bool? hasReachedMax,
    double? zoom,
  }) {
    return BlogsState(
      blogs: blogs ?? this.blogs,
      lastVisible: lastVisible ?? this.lastVisible,
      loadMore: loadMore ?? this.loadMore,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      zoom: zoom ?? this.zoom,
    );
  }
}
