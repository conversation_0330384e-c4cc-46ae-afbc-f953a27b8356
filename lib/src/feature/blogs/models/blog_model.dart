import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';

BlogModel blogModelFromJson(String str) => BlogModel.fromJson(json.decode(str));

String blogModelToJson(BlogModel data) => json.encode(data.toJson());

class BlogModel {
  String? author;
  String? title;
  String? description;
  String? date;
  Timestamp? timestamp;

  BlogModel({
    this.author,
    this.title,
    this.description,
    this.date,
    this.timestamp,
  });

  BlogModel copyWith({
    String? author,
    String? title,
    String? description,
    String? date,
    Timestamp? timestamp,
  }) =>
      BlogModel(
        author: author ?? this.author,
        title: title ?? this.title,
        description: description ?? this.description,
        date: date ?? this.date,
        timestamp: timestamp ?? this.timestamp,
      );

  factory BlogModel.fromJson(Map<String, dynamic> json) => BlogModel(
        author: json["author"],
        title: json["title"],
        description: json["description"],
        date: json["date"],
        timestamp: json["timestamp"],
      );

  Map<String, dynamic> toJson() => {
        "author": author,
        "title": title,
        "description": description,
        "date": date,
        "timestamp": timestamp,
      };
}
