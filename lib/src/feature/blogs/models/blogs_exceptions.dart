abstract class BlogsException implements Exception {
  const BlogsException(this.error, this.stackTrace);

  final Object error;
  final StackTrace stackTrace;
}

class FetchBlogsException extends BlogsException {
  const FetchBlogsException(super.error, super.stackTrace);
}

class BlogsDeserializationException extends BlogsException {
  const BlogsDeserializationException(super.error, super.stackTrace);
}

class AddBlogException extends BlogsException {
  const AddBlogException(super.error, super.stackTrace);
}
