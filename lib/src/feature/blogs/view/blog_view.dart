import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;
import 'package:shridattmandir/src/feature/feature.dart'
    show BlogModel, BlogsCubit, BlogsState;

class BlogView extends StatelessWidget {
  const BlogView({
    super.key,
    required this.arguments,
  });
  final BlogModel arguments;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BlogsCubit, BlogsState>(
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(
            title: const Text(
              'Blog',
              style: TextStyle(
                color: SdmPalette.black,
                fontWeight: FontWeight.bold,
              ),
            ),
            centerTitle: true,
            actions: [
              IconButton(
                onPressed: () {
                  context.read<BlogsCubit>().zoomIn();
                },
                icon: const Icon(
                  Icons.zoom_in,
                ),
              ),
              IconButton(
                onPressed: () {
                  context.read<BlogsCubit>().zoomOut();
                },
                icon: const Icon(Icons.zoom_out),
              ),
            ],
          ),
          body: ListView(
            padding: EdgeInsets.only(
              left: 14.0.w,
              right: 14.w,
              top: 14.h,
              bottom: 40.h,
            ),
            children: <Widget>[
              Text(
                arguments.title!,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 21.sp,
                  color: SdmPalette.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(
                height: 6.h,
              ),
              Text(
                '- ${arguments.author}',
                textAlign: TextAlign.right,
                style: TextStyle(
                  color: SdmPalette.textColorGrey,
                  fontSize: 16.sp,
                ),
              ),
              SizedBox(
                height: 3.h,
              ),
              Text(
                arguments.date!,
                textAlign: TextAlign.right,
                style: TextStyle(
                  color: SdmPalette.textColorGrey,
                  fontSize: 14.sp,
                ),
              ),
              SizedBox(
                height: 14.h,
              ),
              Text(
                arguments.description ?? '',
                textAlign: TextAlign.left,
                style: TextStyle(
                  fontSize: 16.sp * state.zoom,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
