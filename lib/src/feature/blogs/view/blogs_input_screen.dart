import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;
import 'package:shridattmandir/src/feature/feature.dart'
    show BlogsCubit, BlogsState;
import 'package:shridattmandir/src/shared/shared.dart'
    show SdmTextField, SdmPrimaryCta;

class BlogsInput extends StatefulWidget {
  const BlogsInput({super.key});

  @override
  State<BlogsInput> createState() => _BlogsInputState();
}

class _BlogsInputState extends State<BlogsInput> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => BlogsCubit(),
      child: BlocBuilder<BlogsCubit, BlogsState>(
        builder: (context, state) {
          return Scaffold(
            appBar: AppBar(
              title: const Text(
                'Blogs Input',
                style: TextStyle(
                  color: SdmPalette.black,
                  fontWeight: FontWeight.bold,
                ),
              ),
              centerTitle: true,
              iconTheme: const IconThemeData(color: SdmPalette.black),
            ),
            body: SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 30.0,
                ),
                child: Center(
                  child: ListView(
                    shrinkWrap: true,
                    children: [
                      SdmTextField(
                        hintText: 'Author Name (in Marathi)',
                        controller: context.read<BlogsCubit>().authorController,
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      SdmTextField(
                        hintText: 'Date (in Marathi)',
                        controller: context.read<BlogsCubit>().dateController,
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      SdmTextField(
                        hintText: 'Title',
                        controller: context.read<BlogsCubit>().titleController,
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      SdmTextField(
                        hintText: 'Content',
                        controller:
                            context.read<BlogsCubit>().contentController,
                        maxLines: 10,
                      ),
                      const SizedBox(
                        height: 30,
                      ),
                      Center(
                        child: SdmPrimaryCta(
                          onPressed: () async {
                            await context.read<BlogsCubit>().addBlog();
                          },
                          child: state.loadMore
                              ? const CircularProgressIndicator.adaptive()
                              : const Padding(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 30.0,
                                  ),
                                  child: Text(
                                    'Upload Blog',
                                    style: TextStyle(
                                      color: SdmPalette.white,
                                    ),
                                  ),
                                ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
