import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:overlay_support/overlay_support.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;

class NotificationOverlay extends StatelessWidget {
  const NotificationOverlay({
    super.key,
    required this.message,
    this.onNotificationTap,
  });

  final RemoteMessage message;
  final VoidCallback? onNotificationTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onNotificationTap,
      child: Safe<PERSON>rea(
        child: Card(
          child: ListTile(
            title: Text(message.notification?.title ?? ""),
            subtitle: Text(message.notification?.body ?? ""),
            trailing: <PERSON><PERSON><PERSON><PERSON><PERSON>(
              icon: const Icon(
                Icons.close,
                color: SdmPalette.black54,
              ),
              onPressed: () {
                OverlaySupportEntry.of(context)?.dismiss();
              },
            ),
          ),
        ),
      ),
    );
  }
}
