import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette, Assets;

class AboutUsScreen extends StatelessWidget {
  const AboutUsScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'About Us',
          style: TextStyle(
            color: SdmPalette.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.only(left: 20, right: 20, top: 10).h,
        child: ListView(
          children: <Widget>[
            Text(
              'OUR HERITAGE',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: SdmPalette.primary,
                fontSize: 18.sp,
              ),
            ),
            <PERSON><PERSON><PERSON><PERSON>(
              height: 20.h,
            ),
            ClipRRect(
              borderRadius: BorderRadius.circular(10).r,
              child: Assets.sdmImages.gurudev.image(
                fit: BoxFit.cover,
              ),
            ),
            Text(
              '''

<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, 
<PERSON><PERSON><PERSON><PERSON>, <PERSON>hagirathi, <PERSON>tta,
<PERSON>huri bows Shri Datta,
Let us bow Madhuri Datta.''',
              style: TextStyle(
                color: SdmPalette.textColorGrey,
                fontWeight: FontWeight.w700,
                fontSize: 14.sp,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              '''
                
                Above is our great heritage. The first two viz., Hansa and Brahma are Tatvadeh, not human beings. Atri-rishi is the former Guru in our heritage. Dattatraya, the sono the follower of Shri Atri propagated widely the teachings of Atri-rishi. You will find at Mahur, in Maharashtra, Dattashika, Atri Ashram and Anusuya Mandir even today which were his main place of propagation. Satguru Dattatraya propagated the theosophy all over the world. We can see Datta temples everywhere.
                Although there were several Satgurus between His Holyness (H.H.) Dattatraya and H. H. Balbhima we consider only last three Satgurus for convenience.
      In this great heritage H H Balbhima Maharaj Sadekar is the grandguru of Her Holyness Madhuri Nath. He was an engineer by profession. He met his Guru H. H. Laxman Maharaj in Indore.
                H. H. Bhagirathinath Vaidya was "Kripabhakta" of H. H. Balbhima, Maharaj. She worked mainly for upliftment of women. She started working as Satguru at the age of 42. She has followers not only in India but also in countries like USA, UK and Africa.
      H. H. Dattatraya Maharaj Cholkar could get H H Bhagirathi's guidance and her holy company for nearly thirty years. Satguru's company and daily spiritual meals nourished H H Dattatraya Maharaj. In spite of his physical disability of blindness, he worked for 30 years from 1959 to 1989. He covered mainly Vidarbha, Pune region and some part of Madhya Pradesh.
                Since H H Madhurinath was working silently, she was unknown till 1994 as Satguru. She used to gather people, arrange spiritual talks of elderly Sadhakas. Besides, she also used to give spiritual talk everyday. She visited many holy places like Mahur, Indore, Pune, Yavatmal, etc. Through these visits and spiritual dialogues with other people, she decided to work only for her Satguru, by spreading knowledge to others. H H Madhurinath is the best Satguru for us but still she is a simple housewife. She practically teaches us how to devote every moment for GOD, while we enjoy our life. We do get peace of mind when we attend lectures of satgurudev and use thoughtful ideas of GOOD BEHAVIOUR in our day to day life.''',
              style: TextStyle(
                color: SdmPalette.textColorGrey,
                fontWeight: FontWeight.w500,
                fontSize: 14.sp,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
