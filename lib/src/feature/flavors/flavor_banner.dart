import 'package:flutter/material.dart';
import 'package:shridattmandir/src/feature/feature.dart' show FlavorConfig;

class FlavorBanner extends StatelessWidget {
  const FlavorBanner({super.key, this.child});

  final Widget? child;

  @override
  Widget build(BuildContext context) {
    if (FlavorConfig.isDevelopment()) {
      return Banner(
        message: '[DEV] SDM',
        textDirection: TextDirection.ltr,
        location: BannerLocation.topEnd,
        color: FlavorConfig.instance.color,
        child: child,
      );
    } else {
      return child ?? const SizedBox.shrink();
    }
  }
}
