import 'package:flutter/material.dart';

/// Creating a list of flavors that we can use to differentiate between
/// environments.
enum Flavor { dev, prod }

class FlavorValues {
  FlavorValues({required this.appName});
  final String appName;
}

/// It's a class that contains a bunch of static variables that are used to
/// configure the app
class FlavorConfig {
  factory FlavorConfig({
    required Flavor flavor,
    Color color = Colors.blue,
    required FlavorValues values,
  }) {
    _instance ??= FlavorConfig._internal(
      flavor,
      color,
      values,
    );
    return _instance!;
  }

  FlavorConfig._internal(this.flavor, this.color, this.values);

  final Color color;
  final Flavor flavor;
  final FlavorValues values;

  static FlavorConfig? _instance;

  static FlavorConfig get instance {
    return _instance!;
  }

  static bool isProduction() => _instance?.flavor == Flavor.prod;

  static bool isDevelopment() => _instance?.flavor == Flavor.dev;
}
