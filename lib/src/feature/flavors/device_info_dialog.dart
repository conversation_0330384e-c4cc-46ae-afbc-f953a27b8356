import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;
import 'package:shridattmandir/src/feature/feature.dart'
    show FlavorConfig, Flavor;

/// This class is a stateless widget that displays a dialog box with information
/// about the device
class DeviceInfoDialog extends StatefulWidget {
  const DeviceInfoDialog({super.key});

  @override
  State<DeviceInfoDialog> createState() => _DeviceInfoDialogState();
}

class _DeviceInfoDialogState extends State<DeviceInfoDialog> {
  late final ({
    String appName,
    String buildNumber,
    String packageName,
    String version,
  }) packageInfo;

  Future<
      ({
        String appName,
        String packageName,
        String version,
        String buildNumber,
      })> getPackageInfo() async {
    final PackageInfo packageInfo = await PackageInfo.fromPlatform();

    return (
      appName: packageInfo.appName,
      packageName: packageInfo.packageName,
      version: packageInfo.version,
      buildNumber: packageInfo.buildNumber,
    );
  }

  @override
  void initState() {
    super.initState();
    getPackageInfo().then((value) {
      setState(() {
        packageInfo = value;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(10.0),
        ),
      ),
      contentPadding: const EdgeInsets.all(10),
      title: Container(
        padding: const EdgeInsets.all(15.0),
        decoration: const BoxDecoration(
          color: SdmPalette.primary,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(10.0),
            topRight: Radius.circular(10.0),
          ),
        ),
        child: const Text(
          'Device Info',
          style: TextStyle(
            color: SdmPalette.white,
          ),
        ),
      ),
      titlePadding: const EdgeInsets.all(0),
      content: _getContent(),
    );
  }

  Widget _getContent() {
    if (Platform.isAndroid) {
      return _androidContent();
    }
    if (Platform.isIOS) {
      return _iOSContent();
    }
    return const Text("You're not on Android neither iOS");
  }

  Widget _androidContent() {
    return FutureBuilder(
        future: DeviceUtils.androidDeviceInfo(),
        builder: (context, AsyncSnapshot<AndroidDeviceInfo> snapshot) {
          if (!snapshot.hasData) return Container();
          AndroidDeviceInfo device = snapshot.data!;
          return SingleChildScrollView(
            child: Column(
              children: <Widget>[
                _buildTile('App name:', packageInfo.appName),
                _buildTile('Package name:', packageInfo.packageName),
                _buildTile('Version:', packageInfo.version),
                _buildTile('Build number:', packageInfo.buildNumber),
                _buildTile(
                    'Flavor:',
                    FlavorConfig.instance.flavor == Flavor.dev
                        ? "dev"
                        : "prod"),
                _buildTile(
                    'Build mode:', DeviceUtils.currentBuildMode().toString()),
                _buildTile('Physical device?:', '${device.isPhysicalDevice}'),
                _buildTile('Manufacturer:', device.manufacturer),
                _buildTile('Model:', device.model),
                _buildTile('Android version:', device.version.release),
                _buildTile('Android SDK:', '${device.version.sdkInt}'),
                _buildTile('Device ID:', device.id),
              ],
            ),
          );
        });
  }

  Widget _iOSContent() {
    return FutureBuilder(
      future: DeviceUtils.iosDeviceInfo(),
      builder: (context, AsyncSnapshot<IosDeviceInfo> snapshot) {
        // final packageInfo =  DeviceUtils.getPackageInfo();

        if (!snapshot.hasData) return Container();
        IosDeviceInfo device = snapshot.data!;
        return SingleChildScrollView(
          child: Column(
            children: <Widget>[
              _buildTile('App name:', packageInfo.appName),
              _buildTile('Package name:', packageInfo.packageName),
              _buildTile('Version:', packageInfo.version),
              _buildTile('Build number:', packageInfo.buildNumber),
              _buildTile('Flavor:',
                  FlavorConfig.instance.flavor == Flavor.dev ? "dev" : "Prod"),
              _buildTile(
                  'Build mode:', DeviceUtils.currentBuildMode().toString()),
              _buildTile('Physical device?:', '${device.isPhysicalDevice}'),
              _buildTile('Model:', device.model),
              _buildTile('iOS version:', device.systemVersion),
              _buildTile('iOS SDK:', device.systemName),
              _buildTile('Device ID:', '${device.identifierForVendor}'),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTile(String key, String value) {
    return Padding(
      padding: const EdgeInsets.all(5.0),
      child: Row(
        children: <Widget>[
          Text(
            key,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(width: 10.0),
          Expanded(
            child: Text(value),
          )
        ],
      ),
    );
  }
}

class DeviceUtils {
  static String currentBuildMode() {
    if (kReleaseMode) {
      return "RELEASE";
    }
    if (kDebugMode) {
      return "DEBUG";
    }
    if (kProfileMode) {
      return "PROFILE";
    }
    return "UNKNOWN";
  }

  static Future<AndroidDeviceInfo> androidDeviceInfo() async {
    DeviceInfoPlugin plugin = DeviceInfoPlugin();
    return plugin.androidInfo;
  }

  static Future<IosDeviceInfo> iosDeviceInfo() async {
    DeviceInfoPlugin plugin = DeviceInfoPlugin();
    return plugin.iosInfo;
  }
}
