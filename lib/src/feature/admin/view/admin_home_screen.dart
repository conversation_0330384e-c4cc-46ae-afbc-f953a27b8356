import 'package:flutter/material.dart';
import 'package:shridattmandir/src/core/core.dart'
    show SdmRouter, SdmPalette, Assets;
import 'package:shridattmandir/src/feature/feature.dart';

class AdminHome extends StatelessWidget {
  const AdminHome({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: const IconThemeData(
          color: SdmPalette.black,
        ),
        title: const Text(
          'Admin',
          style: TextStyle(
            color: SdmPalette.black,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            const SizedBox(
              height: 30,
            ),
            CircleAvatar(
              foregroundImage: Assets.sdmImages.appLogo.provider(),
              radius: 60,
            ),
            const SizedBox(
              height: 10,
            ),
            const Text(
              'Welcome Admin',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(
              height: 60,
            ),
            GridView(
              shrinkWrap: true,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 1.2,
                crossAxisSpacing: 36,
                mainAxisSpacing: 20,
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: 30,
              ),
              children: [
                AdminHomeSection(
                  logo: Assets.sdmImages.quotesLogo.image(
                    height: 40,
                  ),
                  title: 'Quotes',
                  onTap: () {
                    Navigator.pushNamed(
                      context,
                      SdmRouter.quotesInput,
                    );
                  },
                ),
                AdminHomeSection(
                  logo: Assets.sdmIcons.blog.svg(
                    height: 40,
                  ),
                  title: 'Blogs',
                  onTap: () {
                    Navigator.pushNamed(
                      context,
                      SdmRouter.blogInput,
                    );
                  },
                ),
                AdminHomeSection(
                  logo: Assets.sdmIcons.publications.svg(
                    height: 40,
                  ),
                  title: 'Publication',
                  onTap: () {
                    Navigator.pushNamed(
                      context,
                      SdmRouter.publicationInput,
                    );
                  },
                ),
                AdminHomeSection(
                  logo: Assets.sdmIcons.music.svg(
                    height: 40,
                  ),
                  title: 'Music',
                  onTap: () {
                    Navigator.pushNamed(
                      context,
                      SdmRouter.musicInput,
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
