import 'package:flutter/material.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;

class AdminHomeSection extends StatelessWidget {
  const AdminHomeSection({
    super.key,
    required this.logo,
    required this.title,
    required this.onTap,
  });

  final Widget logo;
  final String title;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.0),
          color: SdmPalette.white,
          boxShadow: [
            BoxShadow(
              color: SdmPalette.black.withValues(alpha: 0.2),
              offset: const Offset(0, 4),
              blurRadius: 4,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            logo,
            const SizedBox(
              height: 12,
            ),
            Text(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 20,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
