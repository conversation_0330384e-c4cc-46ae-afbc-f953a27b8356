import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;
import 'package:shridattmandir/src/feature/feature.dart'
    show VideosCubit, VideosState, VideoBox;
import 'package:shridattmandir/src/shared/shared.dart' show SdmNoContent;
import 'package:very_good_infinite_list/very_good_infinite_list.dart';

class VideosScreen extends StatelessWidget {
  const VideosScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Videos',
          style: TextStyle(
            color: SdmPalette.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: BlocProvider(
        create: (context) => VideosCubit(),
        child: Bloc<PERSON>uilder<VideosCubit, VideosState>(
          builder: (context, state) {
            if (state.loading) {
              return const Center(
                child: CircularProgressIndicator.adaptive(),
              );
            }

            return InfiniteList(
              padding: EdgeInsets.only(
                left: 24.w,
                right: 24.w,
                bottom: 30.h,
              ),
              itemCount: state.videos?.length ?? 0,
              onFetchData: context.read<VideosCubit>().fetchVideos,
              itemBuilder: (context, index) {
                final video = state.videos![index];
                return VideoBox(
                  videoModel: video,
                );
              },
              separatorBuilder: (context, index) {
                return SizedBox(
                  height: 20.h,
                );
              },
              hasReachedMax: state.hasReachedMax,
              isLoading: state.loadMore,
              loadingBuilder: (context) {
                return Center(
                  child: SizedBox(
                    height: 28.w,
                    width: 28.w,
                    child: const CircularProgressIndicator.adaptive(),
                  ),
                );
              },
              emptyBuilder: (context) => SdmNoContent(
                ctaText: 'Go Back',
                onCtaTap: () {
                  Navigator.pop(context);
                },
              ),
            );
          },
        ),
      ),
    );
  }
}
