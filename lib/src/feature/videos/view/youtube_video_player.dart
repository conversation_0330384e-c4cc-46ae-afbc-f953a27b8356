import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette, Assets;
import 'package:shridattmandir/src/feature/feature.dart' show VideoModel;
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

class SdmYoutubeVideoPlayer extends StatefulWidget {
  final VideoModel videoModel;

  const SdmYoutubeVideoPlayer({
    super.key,
    required this.videoModel,
  });

  @override
  State<SdmYoutubeVideoPlayer> createState() => _SdmYoutubeVideoPlayerState();
}

class _SdmYoutubeVideoPlayerState extends State<SdmYoutubeVideoPlayer> {
  late YoutubePlayerController _controller;
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _controller = YoutubePlayerController(
      initialVideoId: widget.videoModel.id!,
      flags: const YoutubePlayerFlags(
        mute: false,
        autoPlay: true,
        loop: true,
        enableCaption: true,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return YoutubePlayerBuilder(
      player: YoutubePlayer(
        controller: _controller,
        showVideoProgressIndicator: true,
        progressIndicatorColor: SdmPalette.amber,
        progressColors: const ProgressBarColors(
          playedColor: SdmPalette.lightRed,
          backgroundColor: SdmPalette.lightRed,
        ),
      ),
      builder: (context, player) {
        return Scaffold(
          appBar: AppBar(
            iconTheme: const IconThemeData(color: SdmPalette.black),
            title: const Text(
              'Video',
              style: TextStyle(
                  color: SdmPalette.black, fontWeight: FontWeight.bold),
            ),
            centerTitle: true,
          ),
          body: ListView(
            children: <Widget>[
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Container(
                    margin: const EdgeInsets.all(10).w,
                    padding: const EdgeInsets.all(6).w,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.0).r,
                        color: SdmPalette.white,
                        boxShadow: [
                          BoxShadow(
                            color: SdmPalette.black29,
                            offset: Offset(0, 10.h),
                            blurRadius: 10.r,
                          )
                        ]),
                    child: player,
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                  Text(
                    '- YOU ARE WATCHING -',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 18.sp,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0).w,
                    child: Text(
                      widget.videoModel.title!,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 20.sp,
                        color: SdmPalette.textColorGrey,
                      ),
                    ),
                  ),
                  Container(
                    margin: const EdgeInsets.only(top: 50, bottom: 10).h,
                    width: 80,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.0),
                        color: SdmPalette.transparent,
                        boxShadow: [
                          BoxShadow(
                            color: SdmPalette.black29,
                            offset: Offset(0, 5.h),
                            blurRadius: 30.r,
                          )
                        ]),
                    child: Hero(
                      tag: 'logo',
                      child: Assets.sdmImages.logo.image(
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                  const Text(
                    'co-powered by',
                  ),
                  SizedBox(
                    height: 2.h,
                  ),
                  const Text(
                    'SHRI DATT MANDIR',
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
