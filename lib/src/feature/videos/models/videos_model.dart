import 'dart:convert';

VideoModel videoModelFromJson(String str) =>
    VideoModel.fromJson(json.decode(str));

String videoModelToJson(VideoModel data) => json.encode(data.toJson());

class VideoModel {
  String? thumbnailImage;
  String? id;
  String? title;
  String? embedurl;
  String? description;
  DateTime? publishedAt;

  VideoModel({
    this.thumbnailImage,
    this.id,
    this.title,
    this.embedurl,
    this.description,
    this.publishedAt,
  });

  VideoModel copyWith({
    String? thumbnailImage,
    String? id,
    String? title,
    String? embedurl,
    String? description,
    DateTime? publishedAt,
  }) =>
      VideoModel(
        thumbnailImage: thumbnailImage ?? this.thumbnailImage,
        id: id ?? this.id,
        title: title ?? this.title,
        embedurl: embedurl ?? this.embedurl,
        description: description ?? this.description,
        publishedAt: publishedAt ?? this.publishedAt,
      );

  factory VideoModel.fromJson(Map<String, dynamic> json) => VideoModel(
        thumbnailImage: json["thumbnailImage"],
        id: json["id"],
        title: json["title"],
        embedurl: json["embedurl"],
        description: json["description"],
        publishedAt: json["publishedAt"] == null
            ? null
            : DateTime.parse(json["publishedAt"]),
      );

  Map<String, dynamic> toJson() => {
        "thumbnailImage": thumbnailImage,
        "id": id,
        "title": title,
        "embedurl": embedurl,
        "description": description,
        "publishedAt": publishedAt?.toIso8601String(),
      };
}
