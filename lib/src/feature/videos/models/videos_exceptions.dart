abstract class VideosException implements Exception {
  const VideosException(this.error, this.stackTrace);

  final Object error;
  final StackTrace stackTrace;
}

class FetchVideosException extends VideosException {
  const FetchVideosException(super.error, super.stackTrace);
}

class VideosDeserializationException extends VideosException {
  const VideosDeserializationException(super.error, super.stackTrace);
}
