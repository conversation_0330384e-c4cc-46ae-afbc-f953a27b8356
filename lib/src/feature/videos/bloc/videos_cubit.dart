import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/core/core.dart' show CubitExt;
import 'package:shridattmandir/src/feature/feature.dart'
    show VideoRepository, VideoModel;
import 'package:shridattmandir/src/shared/shared.dart' show SdmToast;

part 'videos_state.dart';

class VideosCubit extends Cubit<VideosState> {
  VideosCubit() : super(const VideosState());

  final VideoRepository _videoRepository = VideoRepository(
    FirebaseFirestore.instance,
  );

  Future<void> fetchVideos() async {
    safeEmit(
      state.copyWith(
        loadMore: true,
      ),
    );

    try {
      final videos = await _videoRepository.fetchVideos(
        documentSnapshot: state.lastVisible,
      );
      safeEmit(
        state.copyWith(
          videos: [
            ...state.videos ?? [],
            ...videos.videoModel,
          ],
          lastVisible: videos.lastVisibleDocumentSnapshot,
          hasReachedMax: videos.videoModel.isEmpty,
        ),
      );
    } catch (error) {
      SdmToast.show(
        error.toString(),
      );
    }
    safeEmit(
      state.copyWith(
        loadMore: false,
      ),
    );
  }
}
