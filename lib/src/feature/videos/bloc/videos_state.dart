part of 'videos_cubit.dart';

class VideosState extends Equatable {
  const VideosState({
    this.videos,
    this.loading = false,
    this.loadMore = false,
    this.hasReachedMax = false,
    this.lastVisible,
  });

  final List<VideoModel>? videos;
  final bool loading;
  final bool loadMore;
  final bool hasReachedMax;
  final DocumentSnapshot? lastVisible;

  VideosState copyWith({
    List<VideoModel>? videos,
    bool? loading,
    bool? loadMore,
    bool? hasReachedMax,
    DocumentSnapshot? lastVisible,
  }) {
    return VideosState(
      videos: videos ?? this.videos,
      loading: loading ?? this.loading,
      loadMore: loadMore ?? this.loadMore,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      lastVisible: lastVisible ?? this.lastVisible,
    );
  }

  @override
  List<Object?> get props => [
        videos,
        loading,
        loadMore,
        hasReachedMax,
        lastVisible,
      ];
}
