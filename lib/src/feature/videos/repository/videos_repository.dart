import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show VideoModel, VideosDeserializationException, FetchVideosException;

class VideoRepository {
  const VideoRepository(
    FirebaseFirestore firestore,
  ) : _firestore = firestore;

  final FirebaseFirestore _firestore;

  static const String _collectionName = 'videos';
  static const String _orderBy = 'publishedAt';
  static const int _limit = 4;

  Future<
      ({
        List<VideoModel> videoModel,
        DocumentSnapshot? lastVisibleDocumentSnapshot,
      })> fetchVideos({
    DocumentSnapshot? documentSnapshot,
  }) async {
    try {
      Query query = _firestore
          .collection(_collectionName)
          .orderBy(_orderBy, descending: true)
          .limit(_limit);

      if (documentSnapshot != null) {
        query = query.startAfterDocument(documentSnapshot);
      }

      final QuerySnapshot querySnapshot = await query.get();

      final documents = querySnapshot.docs;
      DocumentSnapshot? lastVisible;

      if (querySnapshot.size > 0) {
        lastVisible = querySnapshot.docs[querySnapshot.size - 1];
      }
      return (
        videoModel: documents.toVideo(),
        lastVisibleDocumentSnapshot: lastVisible
      );
    } on VideosDeserializationException {
      rethrow;
    } on Exception catch (error, stackTrace) {
      throw FetchVideosException(error, stackTrace);
    }
  }
}

extension on List<QueryDocumentSnapshot> {
  List<VideoModel> toVideo() {
    final videos = <VideoModel>[];
    for (final document in this) {
      final data = document.data() as Map<String, dynamic>?;
      if (data != null) {
        try {
          videos.add(VideoModel.fromJson(data));
        } catch (error, stackTrace) {
          throw VideosDeserializationException(error, stackTrace);
        }
      }
    }
    return videos;
  }
}
