import 'dart:async';

import 'package:extended_image/extended_image.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:shridattmandir/src/core/core.dart'
    show Assets, SdmPalette, SdmRouter, SdmUrls;
import 'package:shridattmandir/src/feature/feature.dart'
    show
        RemoteConfigCubit,
        AnalyticsCubit,
        SplashLoadEvent,
        PurchasesCubit,
        UserSessionCubit,
        UserSessionState,
        UserRepository,
        UserManagementService;
import 'package:shridattmandir/src/shared/shared.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  void precacheImages(List<String> urls) async {
    try {
      for (String url in urls) {
        unawaited(
          precacheImage(
            ExtendedNetworkImageProvider(url),
            context,
          ),
        );
      }
    } catch (e) {
      SdmToast.show('Error in precaching images: $e');
    }
  }

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback(
      (_) async {
        precacheImages(
          SdmUrls.allImageUrls,
        );

        final analyticsCubit = context.read<AnalyticsCubit>();
        final remoteConfigCubit = context.read<RemoteConfigCubit>();
        final userSessionCubit = context.read<UserSessionCubit>();

        await analyticsCubit.onTrackAnalyticsEvent(
          SplashLoadEvent(),
        );
        await remoteConfigCubit.fetchAndActivate();

        // Initialize user session based on current auth state
        await _initializeUserSession(userSessionCubit);

        // Navigate based on auth state
        debugPrint(
            'SplashScreen: Initialization complete, triggering navigation');
        await _navigateBasedOnAuthState(userSessionCubit.state);
      },
    );
  }

  Future<void> _initializeUserSession(UserSessionCubit userSessionCubit) async {
    final currentUser = FirebaseAuth.instance.currentUser;
    debugPrint(
        'SplashScreen: Initializing user session. Current user: ${currentUser?.uid}');

    if (currentUser != null) {
      // User is authenticated, setup user session
      try {
        final userRepository = context.read<UserRepository>();
        final userManagementService = UserManagementService(userRepository);

        debugPrint('SplashScreen: Calling setupUserSession');
        final userModel =
            await userManagementService.setupUserSession(currentUser);
        debugPrint('SplashScreen: setupUserSession returned: ${userModel.uid}');

        userSessionCubit.updateUserSession(
          authUser: currentUser,
          userProfile: userModel,
        );
        debugPrint(
            'SplashScreen: User session setup complete for ${currentUser.uid}');
      } catch (e) {
        debugPrint('SplashScreen: Error setting up user session: $e');
        // If there's an error, just update with auth user
        userSessionCubit.updateAuthUser(currentUser);
      }
    } else {
      // User is not authenticated
      debugPrint('SplashScreen: No current user, clearing session');
      userSessionCubit.clearSession();
    }
  }

  Future<void> _navigateBasedOnAuthState(
      UserSessionState userSessionState) async {
    debugPrint(
        'SplashScreen: Navigating based on auth state. Authenticated: ${userSessionState.isAuthenticated}');

    if (userSessionState.isAuthenticated && userSessionState.authUser != null) {
      debugPrint('SplashScreen: User authenticated, navigating to home');
      if (await Purchases.isConfigured) {
        await Purchases.logIn(
          userSessionState.authUser?.uid ?? '',
        );
      }
      if (mounted) {
        Navigator.pushReplacementNamed(
          context,
          SdmRouter.home,
        );
      }
    } else {
      debugPrint('SplashScreen: User not authenticated, navigating to auth');
      if (mounted) {
        Navigator.pushReplacementNamed(
          context,
          SdmRouter.phoneRegistration,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<UserSessionCubit, UserSessionState>(
      listenWhen: (previous, current) =>
          previous.isAuthenticated != current.isAuthenticated ||
          previous.authUser != current.authUser,
      listener: (context, userSessionState) async {
        debugPrint(
            'SplashScreen: UserSession state changed. Authenticated: ${userSessionState.isAuthenticated}');

        if (userSessionState.isAuthenticated &&
            userSessionState.authUser != null) {
          debugPrint('SplashScreen: User authenticated, navigating to home');
          if (await Purchases.isConfigured) {
            await Purchases.logIn(
              userSessionState.authUser?.uid ?? '',
            );
          }
          if (context.mounted) {
            Navigator.pushReplacementNamed(
              context,
              SdmRouter.home,
            );
          }
        } else {
          debugPrint(
              'SplashScreen: User not authenticated, navigating to auth');
          if (context.mounted) {
            Navigator.pushReplacementNamed(
              context,
              SdmRouter.phoneRegistration,
            );
          }
        }
      },
      child: Builder(
        builder: (context) {
          context.watch<PurchasesCubit>().state;
          return Scaffold(
            body: Center(
              child: Column(
                children: [
                  const Spacer(),
                  DecoratedBox(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: SdmPalette.black29,
                          offset: const Offset(0, 5),
                          blurRadius: 30.r,
                        )
                      ],
                    ),
                    child: Assets.sdmImages.logo.image(
                      height: 100.h,
                    ),
                  ),
                  SizedBox(
                    height: 18.h,
                  ),
                  Text(
                    'Shri Datt Mandir',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 18.sp,
                    ),
                  ),
                  const Spacer(),
                  const CircularProgressIndicator.adaptive(),
                  SizedBox(
                    height: 20.h,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
