import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:shridattmandir/src/core/core.dart';

class SdmToast {
  static void show(String message, [StackTrace? stackTrace]) {
    final context = SdmRouter.navigatorKey.currentContext;
    if (context == null) {
      log('SdmToast.show: context is null');
      return;
    }
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
        ),
        animation: CurvedAnimation(
          parent: kAlwaysCompleteAnimation,
          curve: Curves.easeOutQuart,
          reverseCurve: Curves.easeInQuart,
        ),
        duration: Duration(
          seconds: 2,
        ),
        margin: EdgeInsets.all(16),
        padding: const EdgeInsets.symmetric(
          horizontal: 20,
          vertical: 14,
        ),
      ),
    );
    log(message);
    if (stackTrace != null) {
      log(stackTrace.toString());
    }
  }
}
