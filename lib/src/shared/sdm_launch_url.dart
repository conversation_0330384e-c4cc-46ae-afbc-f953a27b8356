import 'package:shridattmandir/src/shared/shared.dart' show SdmToast;
import 'package:url_launcher/url_launcher.dart';

class SdmLaunchUrl {
  SdmLaunchUrl._();

  static Future<void> sdmLaunchUrl(
    String url, {
    LaunchMode mode = LaunchMode.externalApplication,
  }) async {
    if (await canLaunchUrl(
      Uri.parse(url),
    )) {
      await launchUrl(
        Uri.parse(url),
        mode: mode,
      );
    } else {
      SdmToast.show(
        'Could not launch $url',
      );
    }
  }
}
