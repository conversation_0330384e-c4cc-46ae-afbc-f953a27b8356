import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';

class SdmNetworkImage extends StatelessWidget {
  final String? url;
  final double? height;
  final double? width;
  final Widget? errorWidget;
  final Widget? placeHolderWidget;
  final BoxFit? fit;
  final BoxShape? shape;
  final BorderRadius? borderRadius;

  const SdmNetworkImage({
    this.height,
    this.width,
    this.errorWidget,
    this.placeHolderWidget,
    this.fit,
    this.shape,
    this.borderRadius,
    required this.url,
    super.key,
  });
  @override
  Widget build(BuildContext context) {
    try {
      if (url == null || url!.isEmpty) {
        return errorWidget ?? const SizedBox.shrink();
      } else if (url!.contains(".mp4")) {
        return const SizedBox.shrink();
      } else {
        return ExtendedImage.network(
          url!,
          cache: true,
          width: width,
          height: height,
          fit: fit,
          shape: shape,
          borderRadius: borderRadius,
          clearMemoryCacheIfFailed: true,
          loadStateChanged: (ExtendedImageState state) {
            switch (state.extendedImageLoadState) {
              case LoadState.loading:
                return placeHolderWidget ?? const SizedBox.shrink();
              case LoadState.completed:
                return ExtendedRawImage(
                  image: state.extendedImageInfo?.image,
                  width: width,
                  height: height,
                  fit: fit,
                );
              case LoadState.failed:
                return errorWidget ?? const SizedBox.shrink();
            }
          },
        );
      }
    } catch (e) {
      return const SizedBox.shrink();
    }
  }
}
