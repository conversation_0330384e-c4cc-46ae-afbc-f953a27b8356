import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show Assets, SdmPalette;
import 'package:shridattmandir/src/shared/shared.dart' show SdmPrimaryCta;

class SdmNoContent extends StatelessWidget {
  const SdmNoContent(
      {super.key,
      required this.ctaText,
      required this.onCtaTap,
      this.title,
      this.subtitle});

  final String ctaText;
  final VoidCallback onCtaTap;
  final String? title;
  final String? subtitle;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
        top: 20.h,
      ),
      width: double.infinity,
      padding: const EdgeInsets.only(
        left: 28.0,
        right: 28,
      ).w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.0).r,
        color: SdmPalette.white,
        boxShadow: [
          BoxShadow(
            color: SdmPalette.black.withValues(alpha: 0.2),
            offset: const Offset(0, 4),
            blurRadius: 4.r,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 70.h,
          ),
          Assets.sdmImages.noContent.image(
            height: 210.h,
          ),
          SizedBox(
            height: 30.h,
          ),
          Text(
            title ?? 'There is no content',
            style: TextStyle(
              fontWeight: FontWeight.w700,
              fontSize: 20.sp,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(
            height: 10.h,
          ),
          Text(
            subtitle ??
                "Whoops..... This information is not\navailable for a moment",
            textAlign: TextAlign.center,
            style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 14.sp,
              color: SdmPalette.textColorGrey,
            ),
          ),
          SizedBox(
            height: 30.h,
          ),
          SdmPrimaryCta(
            onPressed: onCtaTap,
            child: Padding(
              padding: const EdgeInsets.only(
                left: 20.0,
                right: 20,
              ).w,
              child: Text(
                ctaText,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: SdmPalette.white,
                ),
              ),
            ),
          ),
          SizedBox(
            height: 70.h,
          ),
        ],
      ),
    );
  }
}
