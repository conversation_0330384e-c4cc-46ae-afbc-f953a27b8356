import 'package:flutter/material.dart';
import 'package:shridattmandir/src/core/core.dart'
    show SdmPalette, SdmWebViewArguments;
import 'package:shridattmandir/src/shared/shared.dart' show SdmToast;
import 'package:webview_flutter/webview_flutter.dart';

///* +-------------------------------+
///* |   Common WebView throughout   |
///* +-------------------------------+
class SdmWebView extends StatefulWidget {
  const SdmWebView({
    super.key,
    required this.args,
  });

  final SdmWebViewArguments args;

  @override
  State<SdmWebView> createState() => _SdmWebViewState();
}

class _SdmWebViewState extends State<SdmWebView> {
  late final WebViewController _controller;

  @override
  void initState() {
    super.initState();

    _controller = WebViewController()
      ..clearCache()
      ..clearLocalStorage()
      ..setJavaScriptMode(
        JavaScriptMode.unrestricted,
      )
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: widget.args.onPageFinished,
          onPageStarted: widget.args.onPageStarted,
        ),
      )
      ..setUserAgent(
        widget.args.userAgent,
      )
      ..loadRequest(
        Uri.parse(widget.args.uri),
      );

    if (widget.args.onWebViewCreated != null) {
      widget.args.onWebViewCreated!(_controller);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: const IconThemeData(color: SdmPalette.black),
        title: Text(
          widget.args.title,
          style: const TextStyle(
              color: SdmPalette.black, fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(
              Icons.arrow_back_ios,
            ),
            onPressed: () async {
              if (await _controller.canGoBack()) {
                _controller.goBack();
              } else {
                SdmToast.show(
                  'No more pages to go back',
                );
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.arrow_forward_ios),
            onPressed: () async {
              if (await _controller.canGoForward()) {
                _controller.goForward();
              } else {
                SdmToast.show(
                  'No more pages to go forward',
                );
              }
            },
          ),
        ],
      ),
      body: Container(
        padding: const EdgeInsets.all(5),
        margin: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.0),
          color: SdmPalette.white,
          boxShadow: const [
            BoxShadow(
              color: SdmPalette.black29,
              offset: Offset(
                5,
                5,
              ),
              blurRadius: 10,
            )
          ],
        ),
        child: WebViewWidget(
          controller: _controller,
        ),
      ),
    );
  }
}
