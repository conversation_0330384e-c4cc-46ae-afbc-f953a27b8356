import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;

class SdmShimmerLoader extends StatelessWidget {
  const SdmShimmerLoader({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: SdmPalette.white10,
      highlightColor: SdmPalette.primary.withValues(alpha: .8),
      child: child,
    );
  }
}
