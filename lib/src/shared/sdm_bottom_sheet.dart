import 'package:flutter/material.dart';

Future sdmBottomSheetCanvas(BuildContext context, Widget? child) {
  return showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(
          10.0,
        ),
      ),
    ),
    builder: (context) => child ?? const SizedBox.shrink(),
  );
}
