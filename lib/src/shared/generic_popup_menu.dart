import 'package:flutter/material.dart';

class PopupItem<T> {
  final T value;
  final String label;

  const PopupItem({
    required this.value,
    required this.label,
  });
}

class GenericPopupMenu<T> extends StatelessWidget {
  final List<PopupItem<T>> items;
  final Function(T) onSelected;
  final IconData icon;
  final String tooltip;
  final Color iconColor;

  const GenericPopupMenu({
    super.key,
    required this.items,
    required this.onSelected,
    required this.icon,
    required this.tooltip,
    required this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<T>(
      icon: Icon(
        icon,
        color: iconColor,
      ),
      tooltip: tooltip,
      itemBuilder: (context) => items
          .map(
            (item) => PopupMenuItem<T>(
              value: item.value,
              child: Text(item.label),
            ),
          )
          .toList(),
      onSelected: onSelected,
    );
  }
}
