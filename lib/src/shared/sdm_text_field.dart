import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;

class SdmTextField extends StatelessWidget {
  const SdmTextField({
    super.key,
    this.maxLines,
    this.inputFormatters,
    this.controller,
    this.keyboardType,
    this.hintText,
    this.contentPadding,
    this.enabled = true,
    this.fontSize = 14,
    this.textAlign = TextAlign.start,
    this.maxLength,
    this.onChanged,
  });

  final int? maxLines;
  final List<TextInputFormatter>? inputFormatters;
  final TextEditingController? controller;
  final TextInputType? keyboardType;
  final String? hintText;
  final EdgeInsetsGeometry? contentPadding;
  final bool enabled;
  final double fontSize;
  final TextAlign textAlign;
  final int? maxLength;
  final Function(String)? onChanged;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.0).r,
        color: SdmPalette.white,
        boxShadow: [
          BoxShadow(
            color: SdmPalette.black29,
            offset: const Offset(0, 1),
            blurRadius: 2.r,
          )
        ],
      ),
      child: TextField(
        onChanged: onChanged,
        maxLength: maxLength,
        textAlign: textAlign,
        enabled: enabled,
        maxLines: maxLines,
        style: TextStyle(
          fontSize: fontSize,
        ),
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: TextStyle(
            color: SdmPalette.textColorGrey2,
            fontSize: fontSize,
          ),
          border: InputBorder.none,
          isCollapsed: true,
          contentPadding: contentPadding ??
              EdgeInsets.only(
                top: 10.h,
                bottom: 10.h,
                left: 8.w,
              ),
        ),
        controller: controller,
        inputFormatters: inputFormatters,
        keyboardType: keyboardType,
      ),
    );
  }
}
