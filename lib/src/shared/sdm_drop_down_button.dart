import 'package:flutter/material.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;

class SdmDropDownButton extends StatelessWidget {
  const SdmDropDownButton({
    super.key,
    this.hint,
    this.items,
    this.onChanged,
    this.value,
  });

  final Widget? hint;
  final List<DropdownMenuItem<Object>>? items;
  final void Function(Object?)? onChanged;
  final Object? value;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.0),
        color: SdmPalette.white,
        boxShadow: const [
          BoxShadow(
            color: SdmPalette.black29,
            offset: Offset(0, 1),
            blurRadius: 2,
          )
        ],
      ),
      child: DropdownButton(
        enableFeedback: true,
        value: value,
        isDense: true,
        menuMaxHeight: 300,
        padding: const EdgeInsets.symmetric(
          horizontal: 8,
          vertical: 10,
        ),
        underline: const SizedBox.shrink(),
        isExpanded: true,
        hint: hint,
        borderRadius: BorderRadius.circular(10),
        items: items,
        onChanged: onChanged,
        elevation: 2,
      ),
    );
  }
}
