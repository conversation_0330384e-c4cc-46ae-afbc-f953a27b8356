/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsSdmIconsGen {
  const $AssetsSdmIconsGen();

  /// File path: assets/sdm-icons/blog.svg
  SvgGenImage get blog => const SvgGenImage('assets/sdm-icons/blog.svg');

  /// File path: assets/sdm-icons/calendarEvents.svg
  SvgGenImage get calendarEvents =>
      const SvgGenImage('assets/sdm-icons/calendarEvents.svg');

  /// File path: assets/sdm-icons/certificate.svg
  SvgGenImage get certificate =>
      const SvgGenImage('assets/sdm-icons/certificate.svg');

  /// File path: assets/sdm-icons/contactUs.svg
  SvgGenImage get contactUs =>
      const SvgGenImage('assets/sdm-icons/contactUs.svg');

  /// File path: assets/sdm-icons/facebook.svg
  SvgGenImage get facebook =>
      const SvgGenImage('assets/sdm-icons/facebook.svg');

  /// File path: assets/sdm-icons/instagram.svg
  SvgGenImage get instagram =>
      const SvgGenImage('assets/sdm-icons/instagram.svg');

  /// File path: assets/sdm-icons/location.svg
  SvgGenImage get location =>
      const SvgGenImage('assets/sdm-icons/location.svg');

  /// File path: assets/sdm-icons/login.svg
  SvgGenImage get login => const SvgGenImage('assets/sdm-icons/login.svg');

  /// File path: assets/sdm-icons/music.svg
  SvgGenImage get music => const SvgGenImage('assets/sdm-icons/music.svg');

  /// File path: assets/sdm-icons/publications.svg
  SvgGenImage get publications =>
      const SvgGenImage('assets/sdm-icons/publications.svg');

  /// File path: assets/sdm-icons/twitter.svg
  SvgGenImage get twitter => const SvgGenImage('assets/sdm-icons/twitter.svg');

  /// File path: assets/sdm-icons/userIcon.svg
  SvgGenImage get userIcon =>
      const SvgGenImage('assets/sdm-icons/userIcon.svg');

  /// File path: assets/sdm-icons/video.svg
  SvgGenImage get video => const SvgGenImage('assets/sdm-icons/video.svg');

  /// File path: assets/sdm-icons/webGlobe.svg
  SvgGenImage get webGlobe =>
      const SvgGenImage('assets/sdm-icons/webGlobe.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    blog,
    calendarEvents,
    certificate,
    contactUs,
    facebook,
    instagram,
    location,
    login,
    music,
    publications,
    twitter,
    userIcon,
    video,
    webGlobe,
  ];
}

class $AssetsSdmImagesGen {
  const $AssetsSdmImagesGen();

  /// File path: assets/sdm-images/account.png
  AssetGenImage get account =>
      const AssetGenImage('assets/sdm-images/account.png');

  /// File path: assets/sdm-images/appLogo.png
  AssetGenImage get appLogo =>
      const AssetGenImage('assets/sdm-images/appLogo.png');

  /// File path: assets/sdm-images/event_cal.png
  AssetGenImage get eventCal =>
      const AssetGenImage('assets/sdm-images/event_cal.png');

  /// File path: assets/sdm-images/gurudev.png
  AssetGenImage get gurudev =>
      const AssetGenImage('assets/sdm-images/gurudev.png');

  /// File path: assets/sdm-images/logo.png
  AssetGenImage get logo => const AssetGenImage('assets/sdm-images/logo.png');

  /// File path: assets/sdm-images/musicBar.gif
  AssetGenImage get musicBar =>
      const AssetGenImage('assets/sdm-images/musicBar.gif');

  /// File path: assets/sdm-images/no-content.png
  AssetGenImage get noContent =>
      const AssetGenImage('assets/sdm-images/no-content.png');

  /// File path: assets/sdm-images/otpIllustration.png
  AssetGenImage get otpIllustration =>
      const AssetGenImage('assets/sdm-images/otpIllustration.png');

  /// File path: assets/sdm-images/quotesLogo.png
  AssetGenImage get quotesLogo =>
      const AssetGenImage('assets/sdm-images/quotesLogo.png');

  /// File path: assets/sdm-images/registratioIllustration.png
  AssetGenImage get registratioIllustration =>
      const AssetGenImage('assets/sdm-images/registratioIllustration.png');

  /// File path: assets/sdm-images/subscribeIllustration.png
  AssetGenImage get subscribeIllustration =>
      const AssetGenImage('assets/sdm-images/subscribeIllustration.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    account,
    appLogo,
    eventCal,
    gurudev,
    logo,
    musicBar,
    noContent,
    otpIllustration,
    quotesLogo,
    registratioIllustration,
    subscribeIllustration,
  ];
}

class Assets {
  const Assets._();

  static const $AssetsSdmIconsGen sdmIcons = $AssetsSdmIconsGen();
  static const $AssetsSdmImagesGen sdmImages = $AssetsSdmImagesGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter:
          colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
