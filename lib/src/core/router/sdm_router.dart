import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/core/core.dart'
    show MusicPlayerArguments, SdmWebViewArguments;
import 'package:shridattmandir/src/feature/feature.dart';
import 'package:shridattmandir/src/feature/music/view/music-list/music_favorites_screen.dart';
import 'package:shridattmandir/src/shared/shared.dart' show SdmWebView;

class SdmRouter {
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  //*+-------------------------+
  //*|   USER FACING ROUTES    |
  //*+-------------------------+
  static const String splash = '/';
  static const String phoneRegistration = '/phone-registration';
  static const String phoneOtp = '/phone-otp';
  static const String home = '/home';
  static const String aboutUs = '/about-us';
  static const String publications = '/publications';
  static const String publicationInfo = '/publication-info';
  static const String calendarEvents = '/calendar-events';
  static const String blogs = '/blogs';
  static const String blogView = '/blog-view';
  static const String contactUs = '/contact-us';
  static const String videos = '/videos';
  static const String videoPlayer = '/video-player';
  static const String sdmWebView = '/sdm-web-view';
  static const String enterEmail = '/enter-email';
  static const String certificates = '/certificates';
  static const String certificateDetails = '/certificate-details';
  static const String musicPlayer = '/music-player';
  static const String musicList = '/music-list';
  static const String musicPlaylist = '/music-playlist';
  static const String paywall = '/paywall';
  static const String profile = '/profile';

  //*+-------------------------+
  //*|      ADMIN ROUTES       |
  //*+-------------------------+
  static const String adminHome = '/admin-home';
  static const String blogInput = '/blog-input';
  static const String publicationInput = '/publication-input';
  static const String quotesInput = '/quotes-input';
  static const String musicInput = '/music-input';

  SdmRouter(
    MusicRepository musicRepository,
    ProfileRepository profileRepository,
    AccredibleRepository accredibleRepository,
  ) : _musicListCubit = MusicListCubit(
          musicRepository: musicRepository,
          profileRepository: profileRepository,
        );

  final MusicListCubit _musicListCubit;
  final _blogsCubit = BlogsCubit();

  Route onGenerateRoute(RouteSettings settings) {
    final args = settings.arguments;

    switch (settings.name) {
      case splash:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => const SplashScreen(),
        );

      case phoneRegistration:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => const PhoneRegistrationScreen(),
        );

      case phoneOtp:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => const PhoneOtpScreen(),
        );

      case home:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => const SdmHome(),
        );

      case aboutUs:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => const AboutUsScreen(),
        );

      case publications:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => const PublicationsScreen(),
        );

      case publicationInfo:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => PublicationInfo(
            publicationModel: args as PublicationModel,
          ),
        );

      case calendarEvents:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => const CalendarEventsScreen(),
        );

      case blogs:
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => BlocProvider.value(
            value: _blogsCubit,
            child: const BlogsScreen(),
          ),
        );

      case blogView:
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => BlocProvider.value(
            value: _blogsCubit,
            child: BlogView(
              arguments: args as BlogModel,
            ),
          ),
        );

      case contactUs:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => const ContactUsScreen(),
        );

      case musicPlayer:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => MusicPlayer(
            musicPlayerArgs: args as MusicPlayerArguments?,
          ),
        );

      case musicList:
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => BlocProvider.value(
            value: _musicListCubit..fetchFavoritesFromUser(),
            child: const MusicList(),
          ),
        );

      case musicPlaylist:
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => BlocProvider.value(
            value: _musicListCubit,
            child: const MusicFavoritesScreen(),
          ),
        );

      case videos:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => const VideosScreen(),
        );

      case videoPlayer:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => SdmYoutubeVideoPlayer(
            videoModel: args as VideoModel,
          ),
        );

      case sdmWebView:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => SdmWebView(
            args: args as SdmWebViewArguments,
          ),
        );

      case enterEmail:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => const EnterEmailScreen(),
        );

      case certificates:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => CertificatesScreen(
            certificates: args as AccredibleCredentialsCertificates?,
          ),
        );

      case certificateDetails:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => CertificateDetails(
            args: args as Credential,
          ),
        );

      case profile:
        return MaterialPageRoute(
          builder: (context) => const ProfileScreen(),
        );

      case adminHome:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => const AdminHome(),
        );

      case blogInput:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => const BlogsInput(),
        );

      case publicationInput:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => const PublicationsInput(),
        );

      case quotesInput:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => const QuotesInput(),
        );

      case musicInput:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => MusicInputScreen(
            args: args as MusicModel?,
          ),
        );

      default:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => const SdmHome(),
        );
    }
  }

  void dispose() {
    _blogsCubit.disposeControllers();
    _blogsCubit.close();
    _musicListCubit.close();
  }
}
