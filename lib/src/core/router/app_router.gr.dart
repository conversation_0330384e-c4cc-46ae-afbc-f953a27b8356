// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

part of 'app_router.dart';

/// generated route for
/// [HomePage]
class HomeRoute extends PageRouteInfo<void> {
  const HomeRoute({List<PageRouteInfo>? children})
      : super(HomeRoute.name, initialChildren: children);

  static const String name = 'HomeRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const HomePage();
    },
  );
}

/// generated route for
/// [PhoneOtpPage]
class PhoneOtpRoute extends PageRouteInfo<PhoneOtpRouteArgs> {
  PhoneOtpRoute({
    Key? key,
    String? verificationId,
    List<PageRouteInfo>? children,
  }) : super(
          PhoneOtpRoute.name,
          args: PhoneOtpRouteArgs(key: key, verificationId: verificationId),
          initialChildren: children,
        );

  static const String name = 'PhoneOtpRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<PhoneOtpRouteArgs>(
        orElse: () => const PhoneOtpRouteArgs(),
      );
      return PhoneOtpPage(key: args.key, verificationId: args.verificationId);
    },
  );
}

class PhoneOtpRouteArgs {
  const PhoneOtpRouteArgs({this.key, this.verificationId});

  final Key? key;

  final String? verificationId;

  @override
  String toString() {
    return 'PhoneOtpRouteArgs{key: $key, verificationId: $verificationId}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! PhoneOtpRouteArgs) return false;
    return key == other.key && verificationId == other.verificationId;
  }

  @override
  int get hashCode => key.hashCode ^ verificationId.hashCode;
}

/// generated route for
/// [PhoneRegistrationPage]
class PhoneRegistrationRoute extends PageRouteInfo<void> {
  const PhoneRegistrationRoute({List<PageRouteInfo>? children})
      : super(PhoneRegistrationRoute.name, initialChildren: children);

  static const String name = 'PhoneRegistrationRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const PhoneRegistrationPage();
    },
  );
}
