import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show IndexEnum, MusicModel;

class MusicPlayerArguments {
  final List<MusicModel>? musicList;
  final DocumentSnapshot? documentSnapshot;
  final IndexEnum? indexEnum;
  final int? index;
  final int? limit;

  MusicPlayerArguments({
    this.musicList,
    this.documentSnapshot,
    this.index,
    this.indexEnum,
    this.limit,
  });
}
