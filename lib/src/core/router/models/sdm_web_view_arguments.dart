import 'package:webview_flutter/webview_flutter.dart';

class SdmWebViewArguments {
  final String uri;
  final String title;
  final String? userAgent;
  final void Function(String)? onPageFinished;
  final void Function(String)? onPageStarted;
  final void Function(WebViewController controller)? onWebViewCreated;

  SdmWebViewArguments({
    required this.uri,
    required this.title,
    this.userAgent,
    this.onPageFinished,
    this.onPageStarted,
    this.onWebViewCreated,
  });
}
