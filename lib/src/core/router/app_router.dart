import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';

import '../../feature/auth/presentation/screens/phone_otp_screen.dart';
import '../../feature/auth/presentation/screens/phone_registration_screen.dart';
import '../../feature/home/<USER>/sdm_home.dart';

part 'app_router.gr.dart';

/// Auto Route configuration for type-safe navigation
@AutoRouterConfig()
class AppRouter extends _$AppRouter {
  @override
  List<AutoRoute> get routes => [
        // Authentication Routes
        AutoRoute(
          page: PhoneRegistrationRoute.page,
          path: '/auth/phone-registration',
          initial: true,
        ),
        AutoRoute(
          page: PhoneOtpRoute.page,
          path: '/auth/phone-otp',
        ),

        // Main App Routes
        AutoRoute(
          page: HomeRoute.page,
          path: '/home',
        ),

        // Fallback route
        AutoRoute(
          page: PhoneRegistrationRoute.page,
          path: '*',
        ),
      ];
}

/// Phone Registration Page
@RoutePage()
class PhoneRegistrationPage extends StatelessWidget {
  const PhoneRegistrationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const PhoneRegistrationScreen();
  }
}

/// Phone OTP Page
@RoutePage()
class PhoneOtpPage extends StatelessWidget {
  const PhoneOtpPage({
    super.key,
    this.verificationId,
  });

  final String? verificationId;

  @override
  Widget build(BuildContext context) {
    return PhoneOtpScreen(verificationId: verificationId);
  }
}

/// Home Page
@RoutePage()
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return const SdmHome();
  }
}
