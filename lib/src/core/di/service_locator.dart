import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:just_audio/just_audio.dart';

/// Module for registering external dependencies that cannot be annotated
@module
abstract class RegisterModule {
  // Firebase Services
  @singleton
  FirebaseAuth get firebaseAuth => FirebaseAuth.instance;

  @singleton
  FirebaseFirestore get firebaseFirestore => FirebaseFirestore.instance;

  @singleton
  FirebaseStorage get firebaseStorage => FirebaseStorage.instance;

  @singleton
  FirebaseAnalytics get firebaseAnalytics => FirebaseAnalytics.instance;

  @singleton
  FirebaseMessaging get firebaseMessaging => FirebaseMessaging.instance;

  // Device Services
  @singleton
  DeviceInfoPlugin get deviceInfoPlugin => DeviceInfoPlugin();

  // Audio Services
  @singleton
  AudioPlayer get audioPlayer => AudioPlayer();

  // HTTP Client
  @singleton
  Dio get dio {
    final dio = Dio();

    // Add interceptors for logging, authentication, etc.
    dio.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (object) {
          // Use your preferred logging mechanism
          // TODO: Replace with proper logging
          debugPrint(object.toString());
        },
      ),
    );

    return dio;
  }
}
