// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:ui' as _i264;

import 'package:cloud_firestore/cloud_firestore.dart' as _i974;
import 'package:device_info_plus/device_info_plus.dart' as _i833;
import 'package:dio/dio.dart' as _i361;
import 'package:file_picker/file_picker.dart' as _i388;
import 'package:firebase_analytics/firebase_analytics.dart' as _i398;
import 'package:firebase_auth/firebase_auth.dart' as _i59;
import 'package:firebase_messaging/firebase_messaging.dart' as _i892;
import 'package:firebase_storage/firebase_storage.dart' as _i457;
import 'package:flutter/cupertino.dart' as _i719;
import 'package:flutter/foundation.dart' as _i971;
import 'package:flutter/material.dart' as _i409;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:just_audio/just_audio.dart' as _i501;
import 'package:purchases_flutter/purchases_flutter.dart' as _i545;

import '../../feature/about-us/view/about_us_screen.dart' as _i616;
import '../../feature/admin/view/admin_home_screen.dart' as _i618;
import '../../feature/admin/widgets/admin_home_section.dart' as _i300;
import '../../feature/analytics/cubit/analytics_cubit.dart' as _i1020;
import '../../feature/analytics/models/analytics_event.dart' as _i271;
import '../../feature/analytics/models/sdm_events.dart' as _i437;
import '../../feature/analytics/repository/analytics_repository.dart' as _i878;
import '../../feature/app/view/app.dart' as _i523;
import '../../feature/auth/bloc/auth_cubit.dart' as _i998;
import '../../feature/auth/repository/auth_repository.dart' as _i164;
import '../../feature/auth/view/phone_otp_screen.dart' as _i915;
import '../../feature/auth/view/phone_registration_screen.dart' as _i1049;
import '../../feature/blogs/bloc/blogs_cubit.dart' as _i743;
import '../../feature/blogs/models/blog_model.dart' as _i762;
import '../../feature/blogs/models/blogs_exceptions.dart' as _i657;
import '../../feature/blogs/repository/blogs_repository.dart' as _i816;
import '../../feature/blogs/view/blog_view.dart' as _i870;
import '../../feature/blogs/view/blogs_input_screen.dart' as _i938;
import '../../feature/blogs/view/blogs_screen.dart' as _i801;
import '../../feature/blogs/widgets/blog_box.dart' as _i129;
import '../../feature/calendar-events/bloc/calendar_events_cubit.dart' as _i539;
import '../../feature/calendar-events/models/calendar_event_model.dart'
    as _i1041;
import '../../feature/calendar-events/models/calendar_events_exceptions.dart'
    as _i108;
import '../../feature/calendar-events/repository/calendar_events_repository.dart'
    as _i158;
import '../../feature/calendar-events/view/calendar_events_screen.dart'
    as _i280;
import '../../feature/calendar-events/widgets/calendar_event_box.dart' as _i282;
import '../../feature/certificate/bloc/certificate_cubit.dart' as _i657;
import '../../feature/certificate/models/accredible_credentials_certificates_model.dart'
    as _i214;
import '../../feature/certificate/models/accredible_credentials_exceptions.dart'
    as _i201;
import '../../feature/certificate/repository/accredible_repository.dart'
    as _i889;
import '../../feature/certificate/view/certificate_details.dart' as _i126;
import '../../feature/certificate/view/certificates_screen.dart' as _i793;
import '../../feature/certificate/view/enter_email_screen.dart' as _i405;
import '../../feature/certificate/widgets/certificate_box.dart' as _i171;
import '../../feature/contact-us/bloc/contact_us_cubit.dart' as _i1015;
import '../../feature/contact-us/view/contact_us_screen.dart' as _i254;
import '../../feature/contact-us/widgets/social_links.dart' as _i526;
import '../../feature/deeplink/cubit/deeplink_cubit.dart' as _i105;
import '../../feature/feature.dart' as _i688;
import '../../feature/flavors/device_info_dialog.dart' as _i987;
import '../../feature/flavors/flavor_banner.dart' as _i746;
import '../../feature/flavors/flavor_config.dart' as _i547;
import '../../feature/home/<USER>/home_cubit.dart' as _i888;
import '../../feature/home/<USER>/home_quotes_exceptions.dart' as _i783;
import '../../feature/home/<USER>/home_quotes_model.dart' as _i42;
import '../../feature/home/<USER>/home_repository.dart' as _i1056;
import '../../feature/home/<USER>/quotes_input_screen.dart' as _i1036;
import '../../feature/home/<USER>/sdm_home.dart' as _i823;
import '../../feature/home/<USER>/shimmer_skeletons.dart' as _i960;
import '../../feature/home/<USER>/app_drawer.dart' as _i826;
import '../../feature/home/<USER>/drawer_list_tile.dart' as _i316;
import '../../feature/home/<USER>/home_video_box.dart' as _i952;
import '../../feature/home/<USER>/latest_videos.dart' as _i612;
import '../../feature/home/<USER>/profile_completion_card.dart' as _i889;
import '../../feature/home/<USER>/quotes.dart' as _i758;
import '../../feature/home/<USER>/upcoming_events.dart' as _i52;
import '../../feature/music/cubits/music-input-cubit/music_input_cubit.dart'
    as _i740;
import '../../feature/music/cubits/music-list-cubit/music_list_cubit.dart'
    as _i213;
import '../../feature/music/cubits/music-player-cubit/music_player_cubit.dart'
    as _i541;
import '../../feature/music/models/music_exceptions.dart' as _i980;
import '../../feature/music/models/music_model.dart' as _i502;
import '../../feature/music/music.dart' as _i108;
import '../../feature/music/repository/music_repository.dart' as _i605;
import '../../feature/music/view/music-list/music_favorites_screen.dart'
    as _i656;
import '../../feature/music/view/music-list/music_input_screen.dart' as _i753;
import '../../feature/music/view/music-list/music_list.dart' as _i512;
import '../../feature/music/view/music-list/music_lyrics_screen.dart' as _i745;
import '../../feature/music/view/music-player/music_player.dart' as _i447;
import '../../feature/music/widgets/music-list/music_albums_sections.dart'
    as _i713;
import '../../feature/music/widgets/music-list/music_clear_filter_button.dart'
    as _i274;
import '../../feature/music/widgets/music-list/music_index_bottom_sheet.dart'
    as _i960;
import '../../feature/music/widgets/music-list/music_list_tile_leading.dart'
    as _i758;
import '../../feature/music/widgets/music-list/music_list_tile_title.dart'
    as _i770;
import '../../feature/music/widgets/music-list/music_list_tile_trailing.dart'
    as _i867;
import '../../feature/music/widgets/music-list/music_song_list_tile.dart'
    as _i428;
import '../../feature/music/widgets/music-player/music_now_playing_bottom_bar.dart'
    as _i560;
import '../../feature/music/widgets/music-player/player_button.dart' as _i326;
import '../../feature/notifications/view/notification_overlay.dart' as _i822;
import '../../feature/profile/bloc/profile_cubit.dart' as _i712;
import '../../feature/profile/repository/profile_repository.dart' as _i610;
import '../../feature/profile/view/profile_screen.dart' as _i38;
import '../../feature/profile/widget/adaptive_dialog.dart' as _i736;
import '../../feature/publications/bloc/publications_cubit.dart' as _i283;
import '../../feature/publications/models/publication_model.dart' as _i1000;
import '../../feature/publications/models/publications_exceptions.dart'
    as _i209;
import '../../feature/publications/repository/publications_repository.dart'
    as _i517;
import '../../feature/publications/view/publication_info.dart' as _i990;
import '../../feature/publications/view/publications_input_screen.dart' as _i29;
import '../../feature/publications/view/publications_screen.dart' as _i273;
import '../../feature/publications/widgets/publication_box.dart' as _i200;
import '../../feature/purchases/bloc/purchases_cubit.dart' as _i873;
import '../../feature/purchases/repository/purchases_repository.dart' as _i699;
import '../../feature/purchases/service/subscription_service.dart' as _i242;
import '../../feature/purchases/view/paywall.dart' as _i573;
import '../../feature/remote-config/bloc/remote_config_cubit.dart' as _i5;
import '../../feature/splash/view/splash_screen.dart' as _i792;
import '../../feature/user/bloc/user_session_cubit.dart' as _i118;
import '../../feature/user/models/user_model.dart' as _i990;
import '../../feature/user/repository/user_repository.dart' as _i8;
import '../../feature/user/services/user_management_service.dart' as _i108;
import '../../feature/user/widgets/user_session_listener.dart' as _i450;
import '../../feature/videos/bloc/videos_cubit.dart' as _i509;
import '../../feature/videos/models/videos_exceptions.dart' as _i509;
import '../../feature/videos/models/videos_model.dart' as _i642;
import '../../feature/videos/repository/videos_repository.dart' as _i948;
import '../../feature/videos/view/videos_screen.dart' as _i707;
import '../../feature/videos/view/youtube_video_player.dart' as _i509;
import '../../feature/videos/widgets/video_box.dart' as _i593;
import '../../shared/sdm_network_image.dart' as _i327;
import '../../shared/sdm_no_content.dart' as _i381;
import '../../shared/sdm_primary_cta.dart' as _i1016;
import '../../shared/sdm_shimmer_loader.dart' as _i246;
import '../../shared/sdm_toast.dart' as _i608;
import '../../shared/sdm_web_view.dart' as _i581;
import '../bloc/bloc_observer.dart' as _i938;
import '../core.dart' as _i351;
import '../data/rest_client.dart' as _i576;
import '../data/sdm_secure_storage.dart' as _i500;
import '../router/models/music_player_arguments.dart' as _i703;
import '../router/sdm_router.dart' as _i579;
import 'service_locator.dart' as _i105;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final registerModule = _$RegisterModule();
    gh.factory<_i576.RestClient>(() => _i576.RestClient());
    gh.factory<_i500.SdmSecureStorage>(() => _i500.SdmSecureStorage());
    gh.factory<_i938.SdmBlocObserver>(() => const _i938.SdmBlocObserver());
    gh.factory<_SdmWebViewState>(() => _SdmWebViewState());
    gh.factory<_i608.SdmToast>(() => _i608.SdmToast());
    gh.factory<_i740.MusicInputCubit>(() => _i740.MusicInputCubit());
    gh.factory<_MusicPlayerState>(() => _MusicPlayerState());
    gh.factory<_MusicListState>(() => _MusicListState());
    gh.factory<_SdmHomeState>(() => _SdmHomeState());
    gh.factory<_i888.HomeCubit>(() => _i888.HomeCubit());
    gh.factory<_BlogsInputState>(() => _BlogsInputState());
    gh.factory<_i743.BlogsCubit>(() => _i743.BlogsCubit());
    gh.factory<_i539.CalendarEventsCubit>(() => _i539.CalendarEventsCubit());
    gh.factory<_SplashScreenState>(() => _SplashScreenState());
    gh.factory<_i699.PurchasesRepository>(() => _i699.PurchasesRepository());
    gh.factory<_AppState>(() => _AppState());
    gh.factory<_i889.AccredibleRepository>(() => _i889.AccredibleRepository());
    gh.factory<_i214.NextPage>(() => _i214.NextPage());
    gh.factory<_EnterEmailScreenState>(() => _EnterEmailScreenState());
    gh.factory<_CertificateDetailsState>(() => _CertificateDetailsState());
    gh.factory<_i998.AuthCubit>(() => _i998.AuthCubit());
    gh.factory<_i283.PublicationsCubit>(() => _i283.PublicationsCubit());
    gh.factory<_i118.UserSessionCubit>(() => _i118.UserSessionCubit());
    gh.factory<_i5.RemoteConfigCubit>(() => _i5.RemoteConfigCubit());
    gh.factory<_DeviceInfoDialogState>(() => _DeviceInfoDialogState());
    gh.factory<_i987.DeviceUtils>(() => _i987.DeviceUtils());
    gh.factory<_SdmYoutubeVideoPlayerState>(
        () => _SdmYoutubeVideoPlayerState());
    gh.factory<_i509.VideosCubit>(() => _i509.VideosCubit());
    gh.factory<_i712.ProfileCubit>(() => _i712.ProfileCubit());
    gh.factory<_i1015.ContactUsCubit>(() => _i1015.ContactUsCubit());
    gh.factory<_i1015.ContactUsState>(() => const _i1015.ContactUsState());
    gh.factory<_i1020.AnalyticsInitial>(() => _i1020.AnalyticsInitial());
    gh.factory<_i437.SplashLoadEvent>(() => _i437.SplashLoadEvent());
    gh.factory<_i437.SendOtpClickEvent>(() => _i437.SendOtpClickEvent());
    gh.factory<_i437.VerifyOtpClickEvent>(() => _i437.VerifyOtpClickEvent());
    gh.factory<_i437.ResendOtpClickEvent>(() => _i437.ResendOtpClickEvent());
    gh.factory<_i437.HomeLoadEvent>(() => _i437.HomeLoadEvent());
    gh.factory<_i437.AboutUsClickEvent>(() => _i437.AboutUsClickEvent());
    gh.factory<_i437.ProfileClickEvent>(() => _i437.ProfileClickEvent());
    gh.factory<_i437.LogoutClickEvent>(() => _i437.LogoutClickEvent());
    gh.factory<_i437.DeleteAccountClickEvent>(
        () => _i437.DeleteAccountClickEvent());
    gh.factory<_i437.MusicClickEvent>(() => _i437.MusicClickEvent());
    gh.factory<_i437.MusicPaywallSheetOpenEvent>(
        () => _i437.MusicPaywallSheetOpenEvent());
    gh.factory<_i437.VideosClickEvent>(() => _i437.VideosClickEvent());
    gh.factory<_i437.CalendarClickEvent>(() => _i437.CalendarClickEvent());
    gh.factory<_i437.BlogsClickEvent>(() => _i437.BlogsClickEvent());
    gh.factory<_i437.PublicationsClickEvent>(
        () => _i437.PublicationsClickEvent());
    gh.factory<_i437.CertificatesClickEvent>(
        () => _i437.CertificatesClickEvent());
    gh.factory<_i437.ContactUsClickEvent>(() => _i437.ContactUsClickEvent());
    gh.factory<_i437.InfoClickEvent>(() => _i437.InfoClickEvent());
    gh.singleton<_i59.FirebaseAuth>(() => registerModule.firebaseAuth);
    gh.singleton<_i974.FirebaseFirestore>(
        () => registerModule.firebaseFirestore);
    gh.singleton<_i457.FirebaseStorage>(() => registerModule.firebaseStorage);
    gh.singleton<_i398.FirebaseAnalytics>(
        () => registerModule.firebaseAnalytics);
    gh.singleton<_i892.FirebaseMessaging>(
        () => registerModule.firebaseMessaging);
    gh.singleton<_i833.DeviceInfoPlugin>(() => registerModule.deviceInfoPlugin);
    gh.singleton<_i501.AudioPlayer>(() => registerModule.audioPlayer);
    gh.singleton<_i361.Dio>(() => registerModule.dio);
    gh.factory<_i381.SdmNoContent>(() => _i381.SdmNoContent(
          key: gh<_i409.Key>(),
          ctaText: gh<String>(),
          onCtaTap: gh<_i264.VoidCallback>(),
          title: gh<String>(),
          subtitle: gh<String>(),
        ));
    gh.factory<_i509.VideosState>(() => _i509.VideosState(
          videos: gh<List<_i688.VideoModel>>(),
          loading: gh<bool>(),
          loadMore: gh<bool>(),
          hasReachedMax: gh<bool>(),
          lastVisible: gh<_i974.DocumentSnapshot<Object?>>(),
        ));
    gh.factory<_i1056.HomeRepository>(
        () => _i1056.HomeRepository(gh<_i974.FirebaseFirestore>()));
    gh.factory<_i816.BlogsRepository>(
        () => _i816.BlogsRepository(gh<_i974.FirebaseFirestore>()));
    gh.factory<_i158.CalendarEventsRepository>(
        () => _i158.CalendarEventsRepository(gh<_i974.FirebaseFirestore>()));
    gh.factory<_i517.PublicationsRepository>(
        () => _i517.PublicationsRepository(gh<_i974.FirebaseFirestore>()));
    gh.factory<_i8.UserRepository>(
        () => _i8.UserRepository(gh<_i974.FirebaseFirestore>()));
    gh.factory<_i948.VideoRepository>(
        () => _i948.VideoRepository(gh<_i974.FirebaseFirestore>()));
    gh.factory<_i878.AnalyticsRepository>(
        () => _i878.AnalyticsRepository(gh<_i398.FirebaseAnalytics>()));
    gh.factory<_i753.MusicInputScreen>(() => _i753.MusicInputScreen(
          key: gh<_i409.Key>(),
          args: gh<_i688.MusicModel>(),
        ));
    gh.factory<_i998.AuthState>(() => _i998.AuthState(
          loading: gh<bool>(),
          verificationId: gh<String>(),
          forceResendingToken: gh<int>(),
          user: gh<_i59.User>(),
          authStateEnum: gh<_i688.AuthStateEnum>(),
          showResendOtp: gh<bool>(),
        ));
    gh.factory<_i437.MusicPlayEvent>(() => _i437.MusicPlayEvent(
          musicName: gh<String>(),
          id: gh<String>(),
        ));
    gh.factory<_i512.MusicList>(() => _i512.MusicList(key: gh<_i409.Key>()));
    gh.factory<_i656.MusicFavoritesScreen>(
        () => _i656.MusicFavoritesScreen(key: gh<_i409.Key>()));
    gh.factory<_i745.MusicLyricsScreen>(
        () => _i745.MusicLyricsScreen(key: gh<_i409.Key>()));
    gh.factory<_i560.MusicNowPlayingBottomBar>(
        () => _i560.MusicNowPlayingBottomBar(key: gh<_i409.Key>()));
    gh.factory<_i326.PlayerButton>(
        () => _i326.PlayerButton(key: gh<_i409.Key>()));
    gh.factory<_i713.AlbumsSection>(
        () => _i713.AlbumsSection(key: gh<_i409.Key>()));
    gh.factory<_i960.MusicIndexBottomSheet>(
        () => _i960.MusicIndexBottomSheet(key: gh<_i409.Key>()));
    gh.factory<_i274.MusicClearFilterButton>(
        () => _i274.MusicClearFilterButton(key: gh<_i409.Key>()));
    gh.factory<_i1036.QuotesInput>(
        () => _i1036.QuotesInput(key: gh<_i409.Key>()));
    gh.factory<_i960.UpcomingEventsSkeleton>(
        () => _i960.UpcomingEventsSkeleton(key: gh<_i409.Key>()));
    gh.factory<_i960.LatestVideosSkeleton>(
        () => _i960.LatestVideosSkeleton(key: gh<_i409.Key>()));
    gh.factory<_i823.SdmHome>(() => _i823.SdmHome(key: gh<_i409.Key>()));
    gh.factory<_i889.ProfileCompletionCard>(
        () => _i889.ProfileCompletionCard(key: gh<_i409.Key>()));
    gh.factory<_i52.UpcomingEvents>(
        () => _i52.UpcomingEvents(key: gh<_i409.Key>()));
    gh.factory<_i612.LatestVideos>(
        () => _i612.LatestVideos(key: gh<_i409.Key>()));
    gh.factory<_i826.AppDrawer>(() => _i826.AppDrawer(key: gh<_i409.Key>()));
    gh.factory<_i938.BlogsInput>(() => _i938.BlogsInput(key: gh<_i409.Key>()));
    gh.factory<_i801.BlogsScreen>(
        () => _i801.BlogsScreen(key: gh<_i409.Key>()));
    gh.factory<_i280.CalendarEventsScreen>(
        () => _i280.CalendarEventsScreen(key: gh<_i409.Key>()));
    gh.factory<_i792.SplashScreen>(
        () => _i792.SplashScreen(key: gh<_i409.Key>()));
    gh.factory<_i573.Paywall>(() => _i573.Paywall(key: gh<_i409.Key>()));
    gh.factory<_i523.App>(() => _i523.App(key: gh<_i409.Key>()));
    gh.factory<_i616.AboutUsScreen>(
        () => _i616.AboutUsScreen(key: gh<_i409.Key>()));
    gh.factory<_i405.EnterEmailScreen>(
        () => _i405.EnterEmailScreen(key: gh<_i409.Key>()));
    gh.factory<_i1049.PhoneRegistrationScreen>(
        () => _i1049.PhoneRegistrationScreen(key: gh<_i409.Key>()));
    gh.factory<_i915.PhoneOtpScreen>(
        () => _i915.PhoneOtpScreen(key: gh<_i409.Key>()));
    gh.factory<_i273.PublicationsScreen>(
        () => _i273.PublicationsScreen(key: gh<_i409.Key>()));
    gh.factory<_i29.PublicationsInput>(
        () => _i29.PublicationsInput(key: gh<_i409.Key>()));
    gh.factory<_i618.AdminHome>(() => _i618.AdminHome(key: gh<_i409.Key>()));
    gh.factory<_i987.DeviceInfoDialog>(
        () => _i987.DeviceInfoDialog(key: gh<_i971.Key>()));
    gh.factory<_i707.VideosScreen>(
        () => _i707.VideosScreen(key: gh<_i409.Key>()));
    gh.factory<_i38.ProfileScreen>(
        () => _i38.ProfileScreen(key: gh<_i409.Key>()));
    gh.factory<_i254.ContactUsScreen>(
        () => _i254.ContactUsScreen(key: gh<_i409.Key>()));
    gh.factory<_i502.MusicModel>(() => _i502.MusicModel(
          documentId: gh<String>(),
          audioUrl: gh<String>(),
          uploadDate: gh<_i974.Timestamp>(),
          lyricist: gh<String>(),
          category: gh<String>(),
          indexEnum: gh<_i688.IndexEnum>(),
          title: gh<String>(),
          lyrics: gh<String>(),
          referenceBook: gh<String>(),
          artUrl: gh<String>(),
        ));
    gh.factory<_i42.HomeQuoteModel>(() => _i42.HomeQuoteModel(
          text: gh<String>(),
          timestamp: gh<_i974.Timestamp>(),
        ));
    gh.factory<_i164.AuthRepository>(
        () => _i164.AuthRepository(gh<_i59.FirebaseAuth>()));
    gh.factory<_i873.PurchasesCubit>(() => _i873.PurchasesCubit(
          firebaseAuth: gh<_i59.FirebaseAuth>(),
          purchasesRepository: gh<_i688.PurchasesRepository>(),
          firebaseMessaging: gh<_i892.FirebaseMessaging>(),
          deviceInfoPlugin: gh<_i833.DeviceInfoPlugin>(),
        ));
    gh.factory<_i214.Certificate>(
        () => _i214.Certificate(image: gh<_i214.CertificateImage>()));
    gh.factory<_i512.MusicItem>(() => _i512.MusicItem(
          key: gh<_i409.Key>(),
          index: gh<int>(),
        ));
    gh.factory<_i1041.CalendarEventModel>(() => _i1041.CalendarEventModel(
          start: gh<String>(),
          etag: gh<String>(),
          end: gh<String>(),
          title: gh<String>(),
          datetime: gh<DateTime>(),
          datetimeStart: gh<DateTime>(),
        ));
    gh.factory<_i526.SocialLinks>(() => _i526.SocialLinks(
          key: gh<_i409.Key>(),
          text: gh<String>(),
          url: gh<String>(),
          icon: gh<_i409.Widget>(),
          textSize: gh<double>(),
        ));
    gh.factory<_i105.DeeplinkState>(() => _i105.DeeplinkState(
          routeName: gh<String>(),
          arguments: gh<Object>(),
          messageOnForeground: gh<bool>(),
          remoteMessage: gh<_i892.RemoteMessage>(),
        ));
    gh.factory<_i712.ProfileState>(() => _i712.ProfileState(
          userModel: gh<_i688.UserModel>(),
          loading: gh<bool>(),
        ));
    gh.factory<_i214.Badge>(() => _i214.Badge(image: gh<_i214.BadgeImage>()));
    gh.factory<_i1000.PublicationModel>(() => _i1000.PublicationModel(
          title: gh<String>(),
          image: gh<String>(),
          description: gh<String>(),
          url: gh<String>(),
          timestamp: gh<_i974.Timestamp>(),
        ));
    gh.factory<_i5.RemoteConfigState>(() => _i5.RemoteConfigState(
          remoteConfig: gh<Map<_i688.RemoteConfigEnum, dynamic>>(),
          categories: gh<List<String>>(),
          referenceBooks: gh<List<String>>(),
          homeImages: gh<List<String>>(),
          autoPlayLimit: gh<int>(),
        ));
    gh.factory<_i610.ProfileRepository>(() => _i610.ProfileRepository(
          gh<_i974.FirebaseFirestore>(),
          subscriptionService: gh<_i688.SubscriptionService>(),
        ));
    gh.factory<_i437.BlogsReadClickEvent>(
        () => _i437.BlogsReadClickEvent(blogName: gh<String>()));
    gh.factory<_i822.NotificationOverlay>(() => _i822.NotificationOverlay(
          key: gh<_i409.Key>(),
          message: gh<_i892.RemoteMessage>(),
          onNotificationTap: gh<_i264.VoidCallback>(),
        ));
    gh.factory<_i118.UserSessionState>(() => _i118.UserSessionState(
          authUser: gh<_i59.User>(),
          userProfile: gh<_i688.UserModel>(),
          isAuthenticated: gh<bool>(),
        ));
    gh.factory<_i242.SubscriptionService>(() =>
        _i242.SubscriptionService(purchasesCubit: gh<_i688.PurchasesCubit>()));
    gh.factory<_i437.HasMusicPremiumEvent>(
        () => _i437.HasMusicPremiumEvent(userId: gh<String>()));
    gh.factory<_i271.AnalyticsEvent>(() => _i271.AnalyticsEvent(
          gh<String>(),
          properties: gh<Map<String, Object>>(),
        ));
    gh.factory<_i980.FetchMusicException>(() => _i980.FetchMusicException(
          gh<Object>(),
          gh<StackTrace>(),
        ));
    gh.factory<_i980.MusicDeserializationException>(
        () => _i980.MusicDeserializationException(
              gh<Object>(),
              gh<StackTrace>(),
            ));
    gh.factory<_i980.AddMusicException>(() => _i980.AddMusicException(
          gh<Object>(),
          gh<StackTrace>(),
        ));
    gh.factory<_i980.UploadMusicException>(() => _i980.UploadMusicException(
          gh<Object>(),
          gh<StackTrace>(),
        ));
    gh.factory<_i783.FetchHomeQuotesException>(
        () => _i783.FetchHomeQuotesException(
              gh<Object>(),
              gh<StackTrace>(),
            ));
    gh.factory<_i783.HomeQuotesDeserializationException>(
        () => _i783.HomeQuotesDeserializationException(
              gh<Object>(),
              gh<StackTrace>(),
            ));
    gh.factory<_i783.AddHomeQuoteException>(() => _i783.AddHomeQuoteException(
          gh<Object>(),
          gh<StackTrace>(),
        ));
    gh.factory<_i657.FetchBlogsException>(() => _i657.FetchBlogsException(
          gh<Object>(),
          gh<StackTrace>(),
        ));
    gh.factory<_i657.BlogsDeserializationException>(
        () => _i657.BlogsDeserializationException(
              gh<Object>(),
              gh<StackTrace>(),
            ));
    gh.factory<_i657.AddBlogException>(() => _i657.AddBlogException(
          gh<Object>(),
          gh<StackTrace>(),
        ));
    gh.factory<_i108.FetchCalendarEventsException>(
        () => _i108.FetchCalendarEventsException(
              gh<Object>(),
              gh<StackTrace>(),
            ));
    gh.factory<_i108.CalendarEventsDeserializationException>(
        () => _i108.CalendarEventsDeserializationException(
              gh<Object>(),
              gh<StackTrace>(),
            ));
    gh.factory<_i201.EmailInvalidException>(() => _i201.EmailInvalidException(
          gh<Object>(),
          gh<StackTrace>(),
        ));
    gh.factory<_i201.NoCredentialsFoundException>(
        () => _i201.NoCredentialsFoundException(
              gh<Object>(),
              gh<StackTrace>(),
            ));
    gh.factory<_i209.FetchPublicationsException>(
        () => _i209.FetchPublicationsException(
              gh<Object>(),
              gh<StackTrace>(),
            ));
    gh.factory<_i209.PublicationsDeserializationException>(
        () => _i209.PublicationsDeserializationException(
              gh<Object>(),
              gh<StackTrace>(),
            ));
    gh.factory<_i209.AddPublicationException>(
        () => _i209.AddPublicationException(
              gh<Object>(),
              gh<StackTrace>(),
            ));
    gh.factory<_i509.FetchVideosException>(() => _i509.FetchVideosException(
          gh<Object>(),
          gh<StackTrace>(),
        ));
    gh.factory<_i509.VideosDeserializationException>(
        () => _i509.VideosDeserializationException(
              gh<Object>(),
              gh<StackTrace>(),
            ));
    gh.factory<_i793.CertificatesScreen>(() => _i793.CertificatesScreen(
          key: gh<_i409.Key>(),
          certificates: gh<_i688.AccredibleCredentialsCertificates>(),
        ));
    gh.factory<_i214.AccredibleCredentialsCertificates>(
        () => _i214.AccredibleCredentialsCertificates(
              credentials: gh<List<_i214.Credential>>(),
              meta: gh<_i214.Meta>(),
            ));
    gh.factory<_i743.BlogsState>(() => _i743.BlogsState(
          blogs: gh<List<_i688.BlogModel>>(),
          loadMore: gh<bool>(),
          lastVisible: gh<_i974.DocumentSnapshot<Object?>>(),
          hasReachedMax: gh<bool>(),
          zoom: gh<double>(),
        ));
    gh.factory<_i736.AdaptiveDialog>(() => _i736.AdaptiveDialog(
          key: gh<_i719.Key>(),
          title: gh<String>(),
          content: gh<String>(),
          confirmText: gh<String>(),
          cancelText: gh<String>(),
          isDestructive: gh<bool>(),
        ));
    gh.factory<_i547.FlavorValues>(
        () => _i547.FlavorValues(appName: gh<String>()));
    gh.factory<_i213.MusicListState>(() => _i213.MusicListState(
          musicList: gh<List<_i688.MusicModel>>(),
          lastVisibleDocumentSnapshot: gh<_i974.DocumentSnapshot<Object?>>(),
          loading: gh<bool>(),
          loadMore: gh<bool>(),
          hasReachedMax: gh<bool>(),
          indexEnum: gh<_i688.IndexEnum>(),
          favoriteMusicDocumentIds: gh<List<String>>(),
          favoritesMusicList: gh<List<_i688.MusicModel?>>(),
          user: gh<_i688.UserModel>(),
        ));
    gh.factory<_i547.FlavorConfig>(() => _i547.FlavorConfig(
          flavor: gh<_i547.Flavor>(),
          color: gh<_i264.Color>(),
          values: gh<_i547.FlavorValues>(),
        ));
    gh.factory<_i437.VideosPlayEvent>(() => _i437.VideosPlayEvent(
          videoName: gh<String>(),
          id: gh<String>(),
        ));
    gh.factory<_i246.SdmShimmerLoader>(() => _i246.SdmShimmerLoader(
          key: gh<_i409.Key>(),
          child: gh<_i409.Widget>(),
        ));
    gh.factory<_i450.UserSessionListener>(() => _i450.UserSessionListener(
          key: gh<_i409.Key>(),
          child: gh<_i409.Widget>(),
        ));
    gh.factory<_i447.MusicPlayer>(() => _i447.MusicPlayer(
          key: gh<_i409.Key>(),
          musicPlayerArgs: gh<_i351.MusicPlayerArguments>(),
        ));
    gh.factory<_i214.Meta>(() => _i214.Meta(
          currentPage: gh<int>(),
          nextPage: gh<_i214.NextPage>(),
          prevPage: gh<_i214.NextPage>(),
          totalPages: gh<int>(),
          totalCount: gh<int>(),
          pageSize: gh<int>(),
        ));
    gh.factory<_i762.BlogModel>(() => _i762.BlogModel(
          author: gh<String>(),
          title: gh<String>(),
          description: gh<String>(),
          date: gh<String>(),
          timestamp: gh<_i974.Timestamp>(),
        ));
    gh.factory<_i428.MusicSongListTile>(() => _i428.MusicSongListTile(
          key: gh<_i409.Key>(),
          musicItem: gh<_i688.MusicModel>(),
          index: gh<int>(),
          isPlaying: gh<bool>(),
          onTap: gh<_i264.VoidCallback>(),
        ));
    gh.factory<_i214.Credential>(() => _i214.Credential(
          id: gh<int>(),
          name: gh<String>(),
          description: gh<String>(),
          approve: gh<bool>(),
          grade: gh<_i214.NextPage>(),
          complete: gh<bool>(),
          issuedOn: gh<DateTime>(),
          allowSupplementalReferences: gh<_i214.NextPage>(),
          allowSupplementalEvidence: gh<_i214.NextPage>(),
          courseLink: gh<String>(),
          customAttributes: gh<_i214.NextPage>(),
          expiredOn: gh<_i214.NextPage>(),
          groupName: gh<String>(),
          groupId: gh<int>(),
          url: gh<String>(),
          encodedId: gh<String>(),
          private: gh<bool>(),
          accredibleInternalId: gh<int>(),
          seoImage: gh<String>(),
          updatedAt: gh<DateTime>(),
          certificate: gh<_i214.Certificate>(),
          badge: gh<_i214.Badge>(),
          metaData: gh<_i214.NextPage>(),
          uuid: gh<String>(),
          issuer: gh<_i214.Issuer>(),
        ));
    gh.factory<_i746.FlavorBanner>(() => _i746.FlavorBanner(
          key: gh<_i409.Key>(),
          child: gh<_i409.Widget>(),
        ));
    gh.factory<_i867.MusicListTileTrailing>(() => _i867.MusicListTileTrailing(
          key: gh<_i409.Key>(),
          musicItem: gh<_i688.MusicModel>(),
          index: gh<int>(),
        ));
    gh.factory<_i541.MusicPlayerState>(() => _i541.MusicPlayerState(
          playerButtonState: gh<_i688.PlayerButtonState>(),
          positionDuration: gh<Duration>(),
          totalDuration: gh<Duration>(),
          bufferedDuration: gh<Duration>(),
          playlistChildren: gh<List<_i688.MusicModel>>(),
          currentMusic: gh<_i688.MusicModel>(),
          playlistCurrentIndex: gh<int>(),
          lastVisibleDocumentSnapshot: gh<_i974.DocumentSnapshot<Object?>>(),
          indexEnum: gh<_i688.IndexEnum>(),
          loopMode: gh<_i688.LoopModeEnum>(),
          autoPlayLimit: gh<int>(),
          playbackSpeed: gh<double>(),
          isShuffleEnabled: gh<bool>(),
          queueSize: gh<int>(),
          initialStartIndex: gh<int>(),
        ));
    gh.factory<_i129.BlogBox>(() => _i129.BlogBox(
          key: gh<_i409.Key>(),
          blogModel: gh<_i688.BlogModel>(),
        ));
    gh.factory<_i873.PurchasesState>(() => _i873.PurchasesState(
          loading: gh<bool>(),
          offerings: gh<_i545.Offerings>(),
          customerInfo: gh<_i545.CustomerInfo>(),
          hasMusicPremium: gh<bool>(),
          stateChange: gh<bool>(),
        ));
    gh.factory<_i1016.SdmPrimaryCta>(() => _i1016.SdmPrimaryCta(
          key: gh<_i409.Key>(),
          onPressed: gh<_i264.VoidCallback>(),
          child: gh<_i409.Widget>(),
          backgroundColor: gh<_i264.Color>(),
        ));
    gh.factory<_i581.SdmWebView>(() => _i581.SdmWebView(
          key: gh<_i409.Key>(),
          args: gh<_i351.SdmWebViewArguments>(),
        ));
    gh.factory<_i740.MusicInputState>(() => _i740.MusicInputState(
          filePickerResult: gh<_i388.FilePickerResult>(),
          loading: gh<bool>(),
          audioUrl: gh<String>(),
          category: gh<String>(),
          indexEnum: gh<_i688.IndexEnum>(),
          referenceBook: gh<String>(),
        ));
    gh.factory<_i888.HomeState>(() => _i888.HomeState(
          loading: gh<bool>(),
          calendarEvents: gh<List<_i688.CalendarEventModel>>(),
          videos: gh<List<_i688.VideoModel>>(),
          homeQuote: gh<_i688.HomeQuoteModel>(),
        ));
    gh.factory<_i282.CalendarEventBox>(() => _i282.CalendarEventBox(
          key: gh<_i409.Key>(),
          eventName: gh<String>(),
          eventDate: gh<String>(),
          endDate: gh<String>(),
          datetime: gh<DateTime>(),
        ));
    gh.factory<_i657.CertificateCubit>(() => _i657.CertificateCubit(
        accredibleRepository: gh<_i688.AccredibleRepository>()));
    gh.factory<_i1020.AnalyticsCubit>(() => _i1020.AnalyticsCubit(
        analyticsRepository: gh<_i688.AnalyticsRepository>()));
    gh.factory<_i642.VideoModel>(() => _i642.VideoModel(
          thumbnailImage: gh<String>(),
          id: gh<String>(),
          title: gh<String>(),
          embedurl: gh<String>(),
          description: gh<String>(),
          publishedAt: gh<DateTime>(),
        ));
    gh.factory<_i283.PublicationsState>(() => _i283.PublicationsState(
          loading: gh<bool>(),
          publications: gh<List<_i688.PublicationModel>>(),
        ));
    gh.factory<_i758.MusicListTileLeading>(() => _i758.MusicListTileLeading(
          key: gh<_i409.Key>(),
          isPlaying: gh<bool>(),
        ));
    gh.factory<_i171.CertificateBox>(() => _i171.CertificateBox(
          key: gh<_i409.Key>(),
          title: gh<String>(),
          thumbnailURL: gh<String>(),
          description: gh<String>(),
          url: gh<String>(),
        ));
    gh.factory<_i214.Issuer>(() => _i214.Issuer(
          name: gh<String>(),
          description: gh<_i214.NextPage>(),
          url: gh<String>(),
        ));
    gh.factory<_i539.CalendarEventsState>(() => _i539.CalendarEventsState(
          calendarEvents: gh<List<_i688.CalendarEventModel>>(),
          loading: gh<bool>(),
        ));
    gh.factory<_i605.MusicRepository>(() => _i605.MusicRepository(
          gh<_i974.FirebaseFirestore>(),
          gh<_i457.FirebaseStorage>(),
        ));
    gh.factory<_i316.DrawerListTile>(() => _i316.DrawerListTile(
          key: gh<_i409.Key>(),
          title: gh<String>(),
          onTap: gh<_i264.VoidCallback>(),
        ));
    gh.factory<_i105.DeeplinkCubit>(() => _i105.DeeplinkCubit(
          purchasesRepository: gh<_i688.PurchasesRepository>(),
          firebaseMessaging: gh<_i892.FirebaseMessaging>(),
          musicRepository: gh<_i688.MusicRepository>(),
          blogsRepository: gh<_i688.BlogsRepository>(),
          subscriptionService: gh<_i688.SubscriptionService>(),
        ));
    gh.factory<_i300.AdminHomeSection>(() => _i300.AdminHomeSection(
          key: gh<_i409.Key>(),
          logo: gh<_i409.Widget>(),
          title: gh<String>(),
          onTap: gh<_i264.VoidCallback>(),
        ));
    gh.factory<_i327.SdmNetworkImage>(() => _i327.SdmNetworkImage(
          height: gh<double>(),
          width: gh<double>(),
          errorWidget: gh<_i409.Widget>(),
          placeHolderWidget: gh<_i409.Widget>(),
          fit: gh<_i409.BoxFit>(),
          shape: gh<_i409.BoxShape>(),
          borderRadius: gh<_i409.BorderRadius>(),
          url: gh<String>(),
          key: gh<_i409.Key>(),
        ));
    gh.factory<_i657.CertificateState>(() => _i657.CertificateState(
          certificates: gh<_i688.AccredibleCredentialsCertificates>(),
          status: gh<_i657.CertificateStatus>(),
          email: gh<String>(),
        ));
    gh.factory<_i214.CertificateImage>(
        () => _i214.CertificateImage(preview: gh<String>()));
    gh.factory<_i214.BadgeImage>(
        () => _i214.BadgeImage(preview: gh<dynamic>()));
    gh.factory<_i758.Quotes>(() => _i758.Quotes(
          key: gh<_i409.Key>(),
          changeQuote: gh<String>(),
        ));
    gh.factory<_i770.MusicListTileTitle>(() => _i770.MusicListTileTitle(
          key: gh<_i409.Key>(),
          musicItem: gh<_i108.MusicModel>(),
          isPlaying: gh<bool>(),
        ));
    gh.factory<_i703.MusicPlayerArguments>(() => _i703.MusicPlayerArguments(
          musicList: gh<List<_i688.MusicModel>>(),
          documentSnapshot: gh<_i974.DocumentSnapshot<Object?>>(),
          index: gh<int>(),
          indexEnum: gh<_i688.IndexEnum>(),
          limit: gh<int>(),
        ));
    gh.factory<_i990.UserModel>(() => _i990.UserModel(
          uid: gh<String>(),
          email: gh<String>(),
          name: gh<String>(),
          photoUrl: gh<String>(),
          phoneNumber: gh<String>(),
          isAdmin: gh<bool>(),
          musicFavorites: gh<List<String>>(),
          fcmToken: gh<String>(),
          hasMusicPremium: gh<bool>(),
        ));
    gh.factory<_i108.UserManagementService>(
        () => _i108.UserManagementService(gh<_i688.UserRepository>()));
    gh.factory<_i990.PublicationInfo>(() => _i990.PublicationInfo(
          key: gh<_i409.Key>(),
          publicationModel: gh<_i688.PublicationModel>(),
        ));
    gh.factory<_i200.PublicationBox>(() => _i200.PublicationBox(
          key: gh<_i409.Key>(),
          publicationModel: gh<_i688.PublicationModel>(),
        ));
    gh.factory<_i126.CertificateDetails>(() => _i126.CertificateDetails(
          key: gh<_i409.Key>(),
          args: gh<_i688.Credential>(),
        ));
    gh.factory<_i870.BlogView>(() => _i870.BlogView(
          key: gh<_i409.Key>(),
          arguments: gh<_i688.BlogModel>(),
        ));
    gh.factory<_i952.HomeVideoBox>(() => _i952.HomeVideoBox(
          key: gh<_i409.Key>(),
          videoModel: gh<_i688.VideoModel>(),
        ));
    gh.factory<_i509.SdmYoutubeVideoPlayer>(() => _i509.SdmYoutubeVideoPlayer(
          key: gh<_i409.Key>(),
          videoModel: gh<_i688.VideoModel>(),
        ));
    gh.factory<_i593.VideoBox>(() => _i593.VideoBox(
          key: gh<_i409.Key>(),
          videoModel: gh<_i688.VideoModel>(),
        ));
    gh.factory<_i579.SdmRouter>(() => _i579.SdmRouter(
          gh<_i688.MusicRepository>(),
          gh<_i688.ProfileRepository>(),
          gh<_i688.AccredibleRepository>(),
        ));
    gh.factory<_i213.MusicListCubit>(() => _i213.MusicListCubit(
          musicRepository: gh<_i688.MusicRepository>(),
          profileRepository: gh<_i688.ProfileRepository>(),
        ));
    gh.factory<_i541.MusicPlayerCubit>(() => _i541.MusicPlayerCubit(
          audioPlayer: gh<_i501.AudioPlayer>(),
          musicRepository: gh<_i688.MusicRepository>(),
        ));
    return this;
  }
}

class _$RegisterModule extends _i105.RegisterModule {}
