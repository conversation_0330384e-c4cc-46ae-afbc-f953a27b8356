import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get_it/get_it.dart';

final GetIt getIt = GetIt.instance;

/// Configure dependencies for the authentication module
/// This is a manual setup that will be replaced with injectable code generation later
void configureDependencies() {
  // Register Firebase services
  getIt.registerSingleton<FirebaseAuth>(FirebaseAuth.instance);
  getIt.registerSingleton<FirebaseFirestore>(FirebaseFirestore.instance);

  // Register HTTP client
  getIt.registerSingleton<Dio>(_createDio());

  // TODO: Add authentication-specific dependencies here
  // This will be expanded as we create the clean architecture layers
}

/// Reset the dependency injection container
/// Useful for testing
void resetDependencies() {
  getIt.reset();
}

/// Create and configure Dio instance
Dio _createDio() {
  final dio = Dio();

  // Add interceptors for logging, authentication, etc.
  dio.interceptors.add(
    LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (object) {
        // Use your preferred logging mechanism
        print(object);
      },
    ),
  );

  return dio;
}
