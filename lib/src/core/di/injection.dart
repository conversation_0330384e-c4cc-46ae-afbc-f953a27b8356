import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get_it/get_it.dart';

import '../../feature/auth/di/auth_di.dart';

final GetIt getIt = GetIt.instance;

/// Configure dependencies for the application
void configureDependencies() {
  // Register core Firebase services
  getIt.registerSingleton<FirebaseAuth>(FirebaseAuth.instance);
  getIt.registerSingleton<FirebaseFirestore>(FirebaseFirestore.instance);

  // Register HTTP client
  getIt.registerSingleton<Dio>(_createDio());

  // Register authentication module dependencies
  AuthDI.registerDependencies(getIt);
}

/// Reset the dependency injection container
/// Useful for testing
void resetDependencies() {
  getIt.reset();
}

/// Create and configure Dio instance
Dio _createDio() {
  final dio = Dio();

  // Add interceptors for logging, authentication, etc.
  dio.interceptors.add(
    LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (object) {
        // Use your preferred logging mechanism
        print(object);
      },
    ),
  );

  return dio;
}
