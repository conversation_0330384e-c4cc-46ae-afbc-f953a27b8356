import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SdmSecureStorage {
  static const keyMusicCacheClearedDateTime =
      'key_music_cache_cleared_date_time';

  static const FlutterSecureStorage _storage = FlutterSecureStorage();

  //string
  static Future<String?> readString({
    required String key,
  }) async {
    return await _storage.read(key: key);
  }

  static Future<void> writeString({
    required String key,
    required String? value,
  }) async {
    await _storage.write(key: key, value: value);
  }

  //bool
  static Future<bool?> readBool({
    required String key,
  }) async {
    final value = await _storage.read(key: key);
    if (value == 'true') {
      return true;
    }
    if (value == 'false') {
      return false;
    }
    if (value == null) {
      return null;
    }
    throw Exception('value stored must be either true or false or null');
  }

  static Future<void> writeBool({
    required String key,
    required bool? value,
  }) async {
    await _storage.write(key: key, value: value.toString());
  }

  //int
  static Future<int?> readInt({
    required String key,
  }) async {
    final value = await _storage.read(key: key);
    if (value == null) {
      return null;
    }
    return int.tryParse(value);
  }

  static Future<void> writeInt({
    required String key,
    required int? value,
  }) async {
    await _storage.write(key: key, value: value?.toString());
  }

  //double
  static Future<double?> readDouble({
    required String key,
  }) async {
    final value = await _storage.read(key: key);
    if (value == null) {
      return null;
    }
    return double.tryParse(value);
  }

  static Future<void> writeDouble({
    required String key,
    required double? value,
  }) async {
    await _storage.write(key: key, value: value?.toString());
  }

  static Future<void> delete({
    required String key,
  }) async {
    await _storage.delete(key: key);
  }

  static Future<void> deleteAll() async {
    await _storage.deleteAll();
  }
}
