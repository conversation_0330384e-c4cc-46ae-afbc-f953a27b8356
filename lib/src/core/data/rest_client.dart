import 'dart:convert';

import 'package:http/http.dart' as http;

class RestClient {
  Future<http.Response> get(
    String url, {
    Map<String, String>? headers,
  }) {
    return http.get(
      Uri.parse(
        url,
      ),
      headers: headers,
    );
  }

  Future<http.Response> post(
    String url, {
    Map<String, String>? headers,
    Object? body,
    Encoding? encoding,
  }) {
    return http.post(
      Uri.parse(url),
      headers: headers,
      body: body,
      encoding: encoding,
    );
  }

  Future<http.Response> put(
    String url, {
    Map<String, String>? headers,
    Object? body,
    Encoding? encoding,
  }) {
    return http.put(
      Uri.parse(url),
      headers: headers,
      body: body,
      encoding: encoding,
    );
  }

  Future<http.Response> delete(
    String url, {
    Map<String, String>? headers,
    Object? body,
    Encoding? encoding,
  }) {
    return http.delete(
      Uri.parse(url),
      headers: headers,
      body: body,
      encoding: encoding,
    );
  }
}
