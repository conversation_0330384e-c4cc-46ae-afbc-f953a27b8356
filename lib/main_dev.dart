import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:shridattmandir/bootstrap.dart';
import 'package:shridattmandir/src/feature/feature.dart';

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(
  RemoteMessage message,
) async {
  await Firebase.initializeApp();

  await setupNativeNotifications();

  debugPrint('Handling a background message ${message.messageId}');
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  FirebaseMessaging.onBackgroundMessage(
    _firebaseMessagingBackgroundHandler,
  );

  await Firebase.initializeApp();

  await setupNativeNotifications();

  FlavorConfig(
    flavor: Flavor.dev,
    color: Colors.deepPurpleAccent,
    values: FlavorValues(
      appName: '[DEV] SDM',
    ),
  );

  bootstrap(
    () => const App(),
  );
}
