import 'dart:async';
import 'dart:developer';

import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/core/core.dart';
import 'package:shridattmandir/src/core/di/injection.dart';

Future<void> bootstrap(FutureOr<Widget> Function() builder) async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize dependency injection
  configureDependencies();

  FlutterError.onError = (details) {};

  Bloc.observer = const SdmBlocObserver();

  FlutterError.onError = (FlutterErrorDetails errorDetails) {
    //? removing images related errors
    //?[Fatal Exception => Bad state: Failed to load]
    //? removing ahead of server error Fatal Exception:
    //? p7.j: java.io.IOException: SERVICE_NOT_AVAILABLE
    //? [https://stackoverflow.com/questions/24263653/java-io-ioexception-service-not-available-in-gcm-client]

    if (!errorDetails.exception.toString().contains(
              "Failed to load http",
            ) &&
        !errorDetails.exception.toString().contains(
              "Failed host lookup",
            ) &&
        !errorDetails.exception.toString().contains(
              "SERVICE_NOT_AVAILABLE",
            )) {
      log(
        errorDetails.exceptionAsString(),
        stackTrace: errorDetails.stack,
      );
      FlutterError.dumpErrorToConsole(errorDetails);
      FirebaseCrashlytics.instance.recordFlutterError(errorDetails);
    }
  };

  runApp(
    await builder(),
  );
}
