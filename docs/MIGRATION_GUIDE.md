# Migration Guide: Old vs New Authentication Architecture

## Overview

This guide helps developers migrate from the old authentication architecture to the new refactored system. It provides side-by-side comparisons and step-by-step migration instructions.

## Key Changes Summary

| Aspect | Old Architecture | New Architecture |
|--------|------------------|------------------|
| **User State** | `authState.userModel` | `userSessionState.userProfile` |
| **Auth Check** | `authState.userModel != null` | `userSessionState.isAuthenticated` |
| **User ID** | `authState.userModel?.uid` | `userSessionState.currentUserId` |
| **Navigation** | In business logic (Cubits) | In UI layer (BlocListeners) |
| **User Data** | Mixed in AuthCubit | Separate UserSessionCubit |
| **Profile Ops** | ProfileRepository | UserRepository |

## Step-by-Step Migration

### 1. **Update State Access**

#### ❌ Old Way:
```dart
// Getting user data
BlocBuilder<AuthCubit, AuthState>(
  builder: (context, authState) {
    final user = authState.userModel;
    if (user == null) {
      return LoginScreen();
    }

    return Text('Welcome ${user.name}');
  },
)

// Checking authentication
final authState = context.watch<AuthCubit>().state;
if (authState.userModel != null) {
  // User is authenticated
}

// Getting user ID
final userId = context.read<AuthCubit>().state.userModel?.uid;
```

#### ✅ New Way:
```dart
// Getting user data
BlocBuilder<UserSessionCubit, UserSessionState>(
  builder: (context, userSessionState) {
    if (!userSessionState.isAuthenticated) {
      return LoginScreen();
    }

    final user = userSessionState.userProfile;
    return Text('Welcome ${user?.name ?? 'User'}');
  },
)

// Checking authentication
final userSessionState = context.watch<UserSessionCubit>().state;
if (userSessionState.isAuthenticated) {
  // User is authenticated
}

// Getting user ID
final userId = context.read<UserSessionCubit>().state.currentUserId;
```

### 2. **Update Permission Checks**

#### ❌ Old Way:
```dart
// Admin check
final authState = context.watch<AuthCubit>().state;
if (authState.userModel?.isAdmin ?? false) {
  return AdminPanel();
}

// Premium check
final authState = context.watch<AuthCubit>().state;
if (authState.userModel?.hasMusicPremium ?? false) {
  return PremiumFeatures();
}
```

#### ✅ New Way:
```dart
// Admin check
final userSessionState = context.watch<UserSessionCubit>().state;
if (userSessionState.isAdmin) {
  return AdminPanel();
}

// Premium check
final userSessionState = context.watch<UserSessionCubit>().state;
if (userSessionState.hasMusicPremium) {
  return PremiumFeatures();
}
```

### 3. **Update Profile Operations**

#### ❌ Old Way:
```dart
// Profile editing
class ProfileCubit extends Cubit<ProfileState> {
  Future<void> updateProfile(UserModel user) async {
    final authCubit = GetIt.instance<AuthCubit>();
    final userId = authCubit.state.userModel?.uid;

    await profileRepository.updateUser(user: user);
    authCubit.updateUserModel(user); // Direct cubit manipulation
  }
}

// Using ProfileRepository
final profileRepository = context.read<ProfileRepository>();
await profileRepository.updateUser(user: updatedUser);
```

#### ✅ New Way:
```dart
// Profile editing
class ProfileCubit extends Cubit<ProfileState> {
  Future<void> updateProfile(UserModel user) async {
    final userSessionCubit = GetIt.instance<UserSessionCubit>();
    final userId = userSessionCubit.state.currentUserId;

    await userRepository.updateUser(user: user);
    userSessionCubit.updateUserProfile(user); // Proper state management
  }
}

// Using UserRepository
final userRepository = context.read<UserRepository>();
await userRepository.updateUser(user: updatedUser);
```

### 4. **Update Navigation Logic**

#### ❌ Old Way:
```dart
// Navigation in AuthCubit (business logic)
class AuthCubit extends Cubit<AuthState> {
  Future<void> signInWithCredential() async {
    // ... authentication logic

    // ❌ Navigation in business logic
    Navigator.pushNamedAndRemoveUntil(
      context,
      SdmRouter.home,
      (route) => false,
    );
  }
}
```

#### ✅ New Way:
```dart
// Navigation in UI layer
class PhoneOtpScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthCubit, AuthState>(
      listenWhen: (previous, current) => previous.user != current.user,
      listener: (context, state) {
        if (state.user != null) {
          Navigator.pushNamedAndRemoveUntil(
            context,
            '/home',
            (route) => false,
          );
        }
      },
      child: BlocBuilder<AuthCubit, AuthState>(
        builder: (context, state) {
          // UI logic only
        },
      ),
    );
  }
}
```

### 5. **Update Logout Implementation**

#### ❌ Old Way:
```dart
// Logout in AuthCubit
class AuthCubit extends Cubit<AuthState> {
  Future<void> signOut() async {
    await _authRepository.signOut();

    // ❌ Direct navigation from business logic
    Navigator.pushNamedAndRemoveUntil(
      context,
      SdmRouter.phoneRegistration,
      (route) => false,
    );
  }
}

// Usage
await context.read<AuthCubit>().signOut();
```

#### ✅ New Way:
```dart
// Logout coordination
Future<void> logout(BuildContext context) async {
  final musicCubit = context.read<MusicPlayerCubit>();
  final purchasesCubit = context.read<PurchasesCubit>();
  final authCubit = context.read<AuthCubit>();
  final userSessionCubit = context.read<UserSessionCubit>();

  // Stop services
  await musicCubit.stop();
  await purchasesCubit.logOut();

  // Clear session
  userSessionCubit.clearSession();
  await authCubit.signOut();

  // Navigate (in UI layer)
  if (context.mounted) {
    Navigator.pushNamedAndRemoveUntil(
      context,
      '/phone-registration',
      (route) => false,
    );
  }
}
```

## Common Migration Patterns

### 1. **Replace AuthCubit User Access**

#### Find and Replace:
```dart
// Find:
context.read<AuthCubit>().state.userModel
context.watch<AuthCubit>().state.userModel
authState.userModel

// Replace with:
context.read<UserSessionCubit>().state.userProfile
context.watch<UserSessionCubit>().state.userProfile
userSessionState.userProfile
```

### 2. **Replace Authentication Checks**

#### Find and Replace:
```dart
// Find:
authState.userModel != null
authState.userModel == null

// Replace with:
userSessionState.isAuthenticated
!userSessionState.isAuthenticated
```

### 3. **Replace User ID Access**

#### Find and Replace:
```dart
// Find:
authState.userModel?.uid
context.read<AuthCubit>().state.userModel?.uid

// Replace with:
userSessionState.currentUserId
context.read<UserSessionCubit>().state.currentUserId
```

### 4. **Replace ProfileRepository with UserRepository**

#### Find and Replace:
```dart
// Find:
ProfileRepository
context.read<ProfileRepository>()

// Replace with:
UserRepository
context.read<UserRepository>()
```

## File-by-File Migration Checklist

### **UI Components** (`.dart` files in `view/` folders)

- [ ] Replace `BlocBuilder<AuthCubit, AuthState>` with `BlocBuilder<UserSessionCubit, UserSessionState>` for user data
- [ ] Update authentication checks: `authState.userModel != null` → `userSessionState.isAuthenticated`
- [ ] Update user data access: `authState.userModel` → `userSessionState.userProfile`
- [ ] Add `BlocListener` for navigation logic if needed
- [ ] Update permission checks to use computed properties

### **Business Logic** (Cubits/Services)

- [ ] Remove navigation logic from cubits
- [ ] Replace `ProfileRepository` with `UserRepository`
- [ ] Update user ID access: `authState.userModel?.uid` → `userSessionState.currentUserId`
- [ ] Use dependency injection for `UserSessionCubit` if needed
- [ ] Update error handling patterns

### **Repositories**

- [ ] Migrate from `ProfileRepository` to `UserRepository`
- [ ] Update method signatures if needed
- [ ] Add error handling for external services

## Testing Migration

### 1. **Update Test Mocks**

#### ❌ Old Way:
```dart
class MockAuthCubit extends MockCubit<AuthState> implements AuthCubit {
  @override
  AuthState get state => AuthState(
    userModel: MockUserModel(),
  );
}
```

#### ✅ New Way:
```dart
class MockUserSessionCubit extends MockCubit<UserSessionState>
    implements UserSessionCubit {
  @override
  UserSessionState get state => UserSessionState(
    authUser: MockUser(),
    userProfile: MockUserModel(),
    isAuthenticated: true,
  );
}
```

### 2. **Update Widget Tests**

#### ❌ Old Way:
```dart
testWidgets('shows user name when authenticated', (tester) async {
  final authCubit = MockAuthCubit();

  await tester.pumpWidget(
    BlocProvider<AuthCubit>.value(
      value: authCubit,
      child: MyWidget(),
    ),
  );

  expect(find.text('Welcome John'), findsOneWidget);
});
```

#### ✅ New Way:
```dart
testWidgets('shows user name when authenticated', (tester) async {
  final userSessionCubit = MockUserSessionCubit();

  await tester.pumpWidget(
    BlocProvider<UserSessionCubit>.value(
      value: userSessionCubit,
      child: MyWidget(),
    ),
  );

  expect(find.text('Welcome John'), findsOneWidget);
});
```

## Validation Checklist

After migration, verify:

- [ ] **Authentication Flow**: Phone → OTP → Home works correctly
- [ ] **User Data Display**: User profile information shows correctly
- [ ] **Permission Checks**: Admin/Premium features work as expected
- [ ] **Logout Flow**: Logout clears session and navigates correctly
- [ ] **Profile Updates**: Profile editing updates both local and remote state
- [ ] **Error Handling**: Graceful degradation for service failures
- [ ] **Navigation**: All navigation happens in UI layer, not business logic
- [ ] **State Management**: No direct cubit manipulation between modules

## Troubleshooting

### **Common Issues:**

1. **"UserSessionCubit not found"**
   - Ensure `UserSessionCubit` is provided in your widget tree
   - Check dependency injection setup

2. **"User data not updating"**
   - Verify you're using `UserSessionCubit.updateUserProfile()` after updates
   - Check that `UserRepository` operations are successful

3. **"Navigation not working"**
   - Ensure navigation logic is moved to UI layer with `BlocListener`
   - Check that state changes are properly triggering listeners

4. **"Authentication state inconsistent"**
   - Verify `UserSessionListener` is properly set up
   - Check that `AuthCubit` is updating the `user` field correctly

### **Debug Steps:**

1. Add debug prints to track state changes
2. Use `BlocObserver` to monitor all bloc events
3. Verify dependency injection setup
4. Check that all required providers are in widget tree

## Quick Reference

### **State Access Cheat Sheet:**
```dart
// OLD → NEW
authState.userModel → userSessionState.userProfile
authState.userModel?.uid → userSessionState.currentUserId
authState.userModel != null → userSessionState.isAuthenticated
authState.userModel?.isAdmin → userSessionState.isAdmin
authState.userModel?.hasMusicPremium → userSessionState.hasMusicPremium
```

### **Repository Migration:**
```dart
// OLD → NEW
ProfileRepository → UserRepository
profileRepository.updateUser() → userRepository.updateUser()
profileRepository.getUser() → userRepository.getUser()
```

### **Navigation Pattern:**
```dart
// OLD: Navigation in business logic ❌
class SomeCubit {
  void someMethod() {
    Navigator.pushNamed(context, '/route');
  }
}

// NEW: Navigation in UI layer ✅
BlocListener<SomeCubit, SomeState>(
  listener: (context, state) {
    if (state.shouldNavigate) {
      Navigator.pushNamed(context, '/route');
    }
  },
  child: YourWidget(),
)
```

## Conclusion

This migration guide provides a comprehensive approach to updating your codebase to use the new authentication architecture. Take it step by step, test thoroughly, and don't hesitate to refer back to the architecture documentation for clarification on design decisions.

The new architecture provides better separation of concerns, improved testability, and more maintainable code. While the migration requires some effort, the long-term benefits make it worthwhile.
