# Implementation Guide: Authentication & User Management

## Quick Start

### 1. **Using UserSessionCubit**

```dart
// Get current user session
final userSessionState = context.watch<UserSessionCubit>().state;

// Check authentication status
if (userSessionState.isAuthenticated) {
  // User is logged in
  final userId = userSessionState.authUser?.uid;
  final userProfile = userSessionState.userProfile;
}

// Check user permissions
if (userSessionState.userProfile?.isAdmin ?? false) {
  // Show admin features
}

if (userSessionState.userProfile?.hasMusicPremium ?? false) {
  // Show premium features
}
```

### 2. **Handling Authentication**

```dart
// In UI components, listen to auth state changes
BlocListener<AuthCubit, AuthState>(
  listenWhen: (previous, current) =>
      previous.authStateEnum != current.authStateEnum,
  listener: (context, state) {
    switch (state.authStateEnum) {
      case AuthStateEnum.verification:
        Navigator.pushNamed(context, '/phone-otp');
        break;
      case AuthStateEnum.authenticated:
        // Handle post-authentication logic
        break;
    }
  },
  child: YourWidget(),
)
```

### 3. **User Data Operations**

```dart
// Get user repository
final userRepository = context.read<UserRepository>();

// Create new user
await userRepository.createUser(user: userModel);

// Update user data
await userRepository.updateUser(user: updatedUserModel);

// Get user data
final user = await userRepository.getUser(uid: userId);
```

## Common Patterns

### 1. **Authentication Flow Implementation**

#### Phone Registration Screen:
```dart
class PhoneRegistrationScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthCubit, AuthState>(
      listenWhen: (previous, current) =>
          previous.authStateEnum != current.authStateEnum,
      listener: (context, state) {
        if (state.authStateEnum == AuthStateEnum.verification) {
          Navigator.pushNamed(context, '/phone-otp');
        }
      },
      child: BlocBuilder<AuthCubit, AuthState>(
        builder: (context, state) {
          return Scaffold(
            // Your UI here
            body: Column(
              children: [
                // Phone input fields
                SdmPrimaryCta(
                  onPressed: () async {
                    await context.read<AuthCubit>().verifyPhoneNumber();
                  },
                  child: Text('Send OTP'),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
```

#### OTP Verification Screen:
```dart
class PhoneOtpScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthCubit, AuthState>(
      listenWhen: (previous, current) => previous.user != current.user,
      listener: (context, state) {
        if (state.user != null) {
          Navigator.pushNamedAndRemoveUntil(
            context,
            '/home',
            (route) => false,
          );
        }
      },
      child: BlocBuilder<AuthCubit, AuthState>(
        builder: (context, state) {
          return Scaffold(
            // Your OTP UI here
            body: Column(
              children: [
                // OTP input field
                SdmPrimaryCta(
                  onPressed: () async {
                    await context.read<AuthCubit>().signInWithCredential();
                  },
                  child: Text('Verify'),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
```

### 2. **User Session Management**

#### Checking User State:
```dart
class HomeScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserSessionCubit, UserSessionState>(
      builder: (context, userSessionState) {
        if (!userSessionState.isAuthenticated) {
          return LoginPrompt();
        }

        return Scaffold(
          appBar: AppBar(
            title: Text('Welcome ${userSessionState.userProfile?.name ?? 'User'}'),
          ),
          body: Column(
            children: [
              if (!userSessionState.hasCompletedProfile)
                ProfileCompletionCard(),

              if (userSessionState.isAdmin)
                AdminPanel(),

              if (userSessionState.hasMusicPremium)
                PremiumMusicFeatures(),

              // Regular content
            ],
          ),
        );
      },
    );
  }
}
```

#### Profile Completion Check:
```dart
class ProfileCompletionCard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserSessionCubit, UserSessionState>(
      builder: (context, userSessionState) {
        final profile = userSessionState.userProfile;

        if (profile?.name != null && profile?.email != null) {
          return SizedBox(); // Profile is complete
        }

        return Card(
          child: Column(
            children: [
              Text('Complete your profile'),
              SdmPrimaryCta(
                onPressed: () async {
                  await Navigator.pushNamed(context, '/profile');
                },
                child: Text('Complete Profile'),
              ),
            ],
          ),
        );
      },
    );
  }
}
```

### 3. **Logout Implementation**

```dart
class ProfileScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Profile content

          SdmPrimaryCta(
            onPressed: () async {
              final shouldLogout = await showDialog<bool>(
                context: context,
                builder: (context) => AlertDialog(
                  title: Text('Logout'),
                  content: Text('Are you sure you want to logout?'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context, false),
                      child: Text('Cancel'),
                    ),
                    TextButton(
                      onPressed: () => Navigator.pop(context, true),
                      child: Text('Logout'),
                    ),
                  ],
                ),
              );

              if (shouldLogout == true && context.mounted) {
                // Stop any ongoing services
                final musicCubit = context.read<MusicPlayerCubit>();
                final purchasesCubit = context.read<PurchasesCubit>();
                final authCubit = context.read<AuthCubit>();
                final userSessionCubit = context.read<UserSessionCubit>();

                await musicCubit.stop();
                await purchasesCubit.logOut();
                userSessionCubit.clearSession();
                await authCubit.signOut();

                // Navigate to auth screen
                if (context.mounted) {
                  Navigator.of(context).pushNamedAndRemoveUntil(
                    '/phone-registration',
                    (route) => false,
                  );
                }
              }
            },
            child: Text('Logout'),
          ),
        ],
      ),
    );
  }
}
```

## Error Handling

### 1. **Repository Error Handling**

```dart
class UserRepository {
  Future<UserModel?> getUser({required String uid}) async {
    try {
      final doc = await _firestore.collection('users').doc(uid).get();

      if (doc.exists) {
        return UserModel.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting user: $e');
      rethrow; // Let the caller handle the error
    }
  }

  Future<bool> hasMusicSubscription() async {
    try {
      // Use SubscriptionService for consistent subscription checks
      final subscriptionService = SubscriptionService(
        purchasesCubit: context.read<PurchasesCubit>(),
      );
      return subscriptionService.hasMusicPremium;
    } catch (e) {
      debugPrint('Error checking music subscription: $e');
      return false; // Graceful degradation
    }
  }
}
```

### 2. **Service Error Handling**

```dart
class UserManagementService {
  Future<UserModel> setupUserSession(User authUser) async {
    try {
      // Main user setup logic
      UserModel? existingUser = await getUserData(authUser.uid);

      if (existingUser == null) {
        existingUser = await createUserFromAuth(authUser);
      } else {
        await updateUserData(existingUser);
      }

      // Non-critical operations with separate error handling
      try {
        if (FlavorConfig.isProduction()) {
          await Purchases.logIn(authUser.uid);
        }
      } catch (e) {
        debugPrint('Error setting up purchases: $e');
        // Don't fail the entire setup if purchases fail
      }

      return existingUser;
    } catch (e) {
      debugPrint('Error setting up user session: $e');
      rethrow;
    }
  }
}
```

## Testing Patterns

### 1. **Unit Testing Cubits**

```dart
void main() {
  group('UserSessionCubit', () {
    late UserSessionCubit cubit;

    setUp(() {
      cubit = UserSessionCubit();
    });

    tearDown(() {
      cubit.close();
    });

    test('initial state is correct', () {
      expect(cubit.state, const UserSessionState());
      expect(cubit.state.isAuthenticated, false);
    });

    test('updateAuthUser updates authentication state', () {
      final user = MockUser();

      cubit.updateAuthUser(user);

      expect(cubit.state.authUser, user);
      expect(cubit.state.isAuthenticated, true);
    });

    test('clearSession resets state', () {
      final user = MockUser();
      final profile = MockUserModel();

      cubit.updateUserSession(authUser: user, userProfile: profile);
      cubit.clearSession();

      expect(cubit.state, const UserSessionState());
      expect(cubit.state.isAuthenticated, false);
    });
  });
}
```

### 2. **Integration Testing**

```dart
void main() {
  group('Authentication Flow Integration', () {
    testWidgets('complete authentication flow', (tester) async {
      await tester.pumpWidget(MyApp());

      // Start at phone registration
      expect(find.byType(PhoneRegistrationScreen), findsOneWidget);

      // Enter phone number
      await tester.enterText(find.byType(TextField), '1234567890');
      await tester.tap(find.text('Send'));
      await tester.pumpAndSettle();

      // Should navigate to OTP screen
      expect(find.byType(PhoneOtpScreen), findsOneWidget);

      // Enter OTP
      await tester.enterText(find.byType(Pinput), '123456');
      await tester.tap(find.text('Verify'));
      await tester.pumpAndSettle();

      // Should navigate to home
      expect(find.byType(SdmHome), findsOneWidget);
    });
  });
}
```

## Performance Considerations

### 1. **Efficient State Management**

```dart
// Use context.select for specific state properties
Widget build(BuildContext context) {
  final isAuthenticated = context.select<UserSessionCubit, bool>(
    (cubit) => cubit.state.isAuthenticated,
  );

  final userName = context.select<UserSessionCubit, String?>(
    (cubit) => cubit.state.userProfile?.name,
  );

  return Text(isAuthenticated ? 'Welcome $userName' : 'Please login');
}
```

### 2. **Avoiding Unnecessary Rebuilds**

```dart
// Use listenWhen and buildWhen to control when widgets rebuild
BlocBuilder<UserSessionCubit, UserSessionState>(
  buildWhen: (previous, current) =>
      previous.userProfile?.name != current.userProfile?.name,
  builder: (context, state) {
    return Text('Welcome ${state.userProfile?.name ?? 'User'}');
  },
)
```

## Debugging Tips

### 1. **Enable Debug Logging**

```dart
// Add debug prints to track state changes
class UserSessionCubit extends Cubit<UserSessionState> {
  void updateUserSession({User? authUser, UserModel? userProfile}) {
    debugPrint('UserSession: Updating session - Auth: ${authUser?.uid}, Profile: ${userProfile?.uid}');

    safeEmit(state.copyWith(
      authUser: () => authUser,
      userProfile: () => userProfile,
      isAuthenticated: authUser != null,
    ));
  }
}
```

### 2. **State Inspection**

```dart
// Use BlocObserver to monitor all state changes
class AppBlocObserver extends BlocObserver {
  @override
  void onChange(BlocBase bloc, Change change) {
    super.onChange(bloc, change);

    if (bloc is AuthCubit || bloc is UserSessionCubit) {
      debugPrint('${bloc.runtimeType}: ${change.currentState}');
    }
  }
}

// In main.dart
void main() {
  Bloc.observer = AppBlocObserver();
  runApp(MyApp());
}
```

This implementation guide provides practical examples and patterns for working with the new authentication architecture. Follow these patterns to maintain consistency and leverage the full benefits of the refactored system.
