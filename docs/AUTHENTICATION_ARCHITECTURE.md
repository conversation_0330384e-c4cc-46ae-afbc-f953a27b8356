# Authentication & User Management Architecture

## Overview

This document describes the refactored authentication and user management architecture implemented to improve separation of concerns, maintainability, and scalability.

## Architecture Principles

### 1. **Separation of Concerns**
- **Authentication**: Handles only Firebase Auth operations (login, logout, OTP)
- **User Management**: Handles user profile data and Firestore operations
- **Session Management**: Coordinates between authentication and user data

### 2. **Clean Architecture**
- **Presentation Layer**: UI components and BlocListeners
- **Business Logic Layer**: Cubits and Services
- **Data Layer**: Repositories and Models

### 3. **Event-Driven Communication**
- Components communicate through state changes rather than direct dependencies
- BlocListeners coordinate between different modules

## Core Components

### 1. Authentication Module (`lib/src/feature/auth/`)

#### **AuthCubit** (`bloc/auth_cubit.dart`)
**Responsibility**: Handles Firebase Authentication operations only

**Key Methods**:
- `verifyPhoneNumber()`: Sends OTP to phone number
- `signInWithCredential()`: Verifies OTP and authenticates user
- `signOut()`: Signs out from Firebase Auth
- `deleteAccount()`: Deletes Firebase Auth account

**State Management**:
```dart
class AuthState {
  final bool loading;
  final String? verificationId;
  final int? forceResendingToken;
  final User? user;                    // Firebase Auth User
  final AuthStateEnum authStateEnum;   // registration | verification | authenticated
  final bool showResendOtp;
}
```

**Key Changes**:
- ✅ Removed profile management responsibilities
- ✅ Removed navigation logic (handled by UI layer)
- ✅ Properly updates `user` field on successful authentication
- ✅ Only handles Firebase Auth operations

#### **AuthRepository** (`repository/auth_repository.dart`)
**Responsibility**: Firebase Auth API wrapper

**Key Methods**:
- `verifyPhoneNumber()`: Firebase phone verification
- `signInWithCredential()`: Firebase credential sign-in
- `signOut()`: Firebase sign out
- `deleteUser()`: Firebase account deletion

### 2. User Module (`lib/src/feature/user/`)

#### **UserSessionCubit** (`bloc/user_session_cubit.dart`)
**Responsibility**: Manages current user session state (combines auth + profile)

**State Management**:
```dart
class UserSessionState {
  final User? authUser;           // Firebase Auth User
  final UserModel? userProfile;   // Firestore User Profile
  final bool isAuthenticated;     // Computed from authUser
}
```

**Key Methods**:
- `updateAuthUser()`: Updates Firebase Auth user
- `updateUserProfile()`: Updates Firestore user profile
- `updateUserSession()`: Updates both auth and profile data
- `clearSession()`: Clears entire user session

**Computed Properties**:
- `currentUserId`: Gets current user ID
- `hasCompletedProfile`: Checks if profile is complete
- `isAdmin`: Checks admin status
- `hasMusicPremium`: Checks premium status

#### **UserRepository** (`repository/user_repository.dart`)
**Responsibility**: Firestore user data operations

**Key Methods**:
- `createUser()`: Creates new user document
- `updateUser()`: Updates existing user document
- `getUser()`: Retrieves user by UID
- `deleteUser()`: Deletes user document
- `updateMusicFavorites()`: Updates user's music preferences

**Features**:
- ✅ Automatic FCM token management
- ✅ Music subscription status integration
- ✅ Error handling for external service failures

#### **UserManagementService** (`services/user_management_service.dart`)
**Responsibility**: Coordinates user operations and session setup

**Key Methods**:
- `setupUserSession()`: Complete user session initialization
- `createUserFromAuth()`: Creates Firestore user from Firebase Auth
- `getUserData()`: Retrieves user data
- `updateUserData()`: Updates user data
- `deleteUserData()`: Deletes user data

**Features**:
- ✅ Handles new user creation
- ✅ Updates existing user data
- ✅ Integrates with Purchases (RevenueCat)
- ✅ Comprehensive error handling

#### **UserSessionListener** (`widgets/user_session_listener.dart`)
**Responsibility**: Coordinates between AuthCubit and UserSessionCubit

**Functionality**:
- Listens to AuthCubit user changes
- Automatically sets up user session when user signs in
- Clears session when user signs out
- Does NOT handle navigation (separation of concerns)

### 3. Profile Module (`lib/src/feature/profile/`)

#### **ProfileCubit** (`bloc/profile_cubit.dart`)
**Responsibility**: Handles profile editing and validation

**Key Changes**:
- ✅ Now uses UserRepository instead of ProfileRepository
- ✅ Focuses only on profile editing operations
- ✅ Gets user ID from UserSessionCubit instead of AuthCubit

## Data Flow

### 1. **App Startup Flow**
```
SplashScreen
├── Initialize UserSessionCubit with current Firebase Auth user
├── If user exists: Setup complete user session
├── Navigate based on authentication state
└── UserSessionListener coordinates ongoing state changes
```

### 2. **Authentication Flow**
```
Phone Registration
├── User enters phone number
├── AuthCubit.verifyPhoneNumber()
├── BlocListener detects authStateEnum.verification
└── Navigate to OTP screen

OTP Verification
├── User enters OTP
├── AuthCubit.signInWithCredential()
├── AuthCubit updates user field
├── BlocListener detects user change
├── Navigate to home
└── UserSessionListener sets up user session
```

### 3. **Logout Flow**
```
Profile Screen
├── User clicks logout
├── Clear UserSessionCubit
├── AuthCubit.signOut()
├── Navigate to auth screen
└── UserSessionListener clears session
```

## Navigation Strategy

### **Separation of Navigation Logic**
- **Business Logic Layer**: NO navigation logic
- **UI Layer**: Handles all navigation through BlocListeners

### **Navigation Points**:
1. **SplashScreen**: Initial navigation based on auth state
2. **PhoneRegistrationScreen**: Navigation to OTP screen
3. **PhoneOtpScreen**: Navigation to home after authentication
4. **ProfileScreen**: Navigation after logout/delete account

## Error Handling Strategy

### **Graceful Degradation**:
- Music subscription check failures don't crash user creation
- FCM token failures don't prevent user session setup
- Purchases setup failures don't block authentication

### **Error Boundaries**:
- Each service handles its own errors
- Critical operations have fallback mechanisms
- User-facing errors are handled gracefully

## Benefits Achieved

### 1. **Better Maintainability**
- Clear separation of responsibilities
- Each component has a single purpose
- Easy to test individual components

### 2. **Improved Scalability**
- Easy to add new authentication methods
- User data operations are centralized
- Session management is flexible

### 3. **Enhanced Testability**
- Each cubit can be tested independently
- Services can be mocked easily
- Clear interfaces between components

### 4. **Better Error Handling**
- Errors are contained within their domains
- Graceful degradation for non-critical features
- User experience is preserved during failures

## Migration Notes

### **Breaking Changes**:
- `authState.userModel` → `userSessionState.userProfile`
- `authCubit.getUserDataFromFirestore()` → Use UserSessionCubit
- Navigation logic moved from business logic to UI layer

### **Backward Compatibility**:
- All existing user data remains compatible
- Firebase Auth integration unchanged
- UI components updated to use new state management

## Future Enhancements

### **Potential Improvements**:
1. **Dependency Injection**: Add proper DI container
2. **Use Cases**: Extract complex business logic into use cases
3. **Domain Models**: Separate domain models from data models
4. **Event Bus**: Add event-driven communication between modules
5. **Offline Support**: Add local storage and sync capabilities

## Conclusion

This architecture provides a solid foundation for authentication and user management that follows clean architecture principles while maintaining simplicity and maintainability. The separation of concerns ensures that each component has a clear responsibility, making the codebase easier to understand, test, and extend.
