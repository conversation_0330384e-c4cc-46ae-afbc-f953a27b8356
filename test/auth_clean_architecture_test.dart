import 'package:flutter_test/flutter_test.dart';
import 'package:shridattmandir/src/feature/auth/auth_clean.dart';

void main() {
  group('Auth Clean Architecture Tests', () {
    test('should validate phone auth request entity', () {
      // Test valid phone auth request
      const validRequest = PhoneAuthRequest(
        phoneNumber: '1234567890',
        countryCode: '+91',
      );

      expect(validRequest.isValid, isTrue);
      expect(validRequest.completePhoneNumber, equals('+************'));

      // Test invalid phone auth request
      const invalidRequest = PhoneAuthRequest(
        phoneNumber: '123', // Too short
        countryCode: '+91',
      );

      expect(invalidRequest.isValid, isFalse);
    });

    test('should validate OTP verification request entity', () {
      // Test valid OTP request
      const validRequest = OtpVerificationRequest(
        verificationId: 'test_verification_id',
        smsCode: '123456',
      );

      expect(validRequest.isValid, isTrue);

      // Test invalid OTP request
      const invalidRequest = OtpVerificationRequest(
        verificationId: 'test_verification_id',
        smsCode: '12', // Too short
      );

      expect(invalidRequest.isValid, isFalse);
    });

    test('should create auth user entity with correct properties', () {
      const authUser = AuthUser(
        uid: 'test_uid',
        phoneNumber: '+************',
        email: '<EMAIL>',
        displayName: 'Test User',
        isEmailVerified: true,
        isPhoneVerified: true,
      );

      expect(authUser.uid, equals('test_uid'));
      expect(authUser.hasBasicProfile, isTrue);
      expect(authUser.isFullyVerified, isTrue);
    });
  });
}
