---
type: "manual"
---

# Flutter App Architecture (Clean Architecture)

## Architecture

1. **Adopt Clean Architecture**: Separate your application into three primary layers: **UI (Presentation) Layer**, **Domain (Logic) Layer**, and **Data Layer**. The Domain Layer encapsulates business logic and use cases, acting as an intermediary between the UI and Data layers.
2. **Organize Code by Feature**: Group related classes for each feature in a dedicated directory (e.g., `auth/` containing `auth_bloc.dart`, `login_usecase.dart`, `login_screen.dart`, etc.). Alternatively, use a hybrid approach combining feature-based and type-based organization for large projects.
3. **Layer Communication**: Ensure communication only between adjacent layers. The UI Layer interacts with the Domain Layer, and the Domain Layer interacts with the Data Layer. Direct access from UI to Data is prohibited.
4. **Introduce Domain Layer**: Include a Domain Layer only when business logic is complex or reused across features. Keep it minimal for simpler apps to avoid unnecessary abstraction.
5. **Define Responsibilities**: Clearly outline responsibilities, boundaries, and interfaces for each layer and component (e.g., Views, Blocs, Repositories, Services).
6. **Component Division**: Subdivide each layer into components with specific responsibilities and well-defined interfaces.
7. **UI Layer**:
   - Use **Views** (implemented as Flutter widgets) to present data to the user with minimal, UI-specific logic.
   - Leverage `StatelessWidget` where possible to optimize performance, reserving `StatefulWidget` for dynamic UI.
   - Extract reusable widgets into separate components to promote modularity.
   - Keep `build` methods simple, focused on rendering, and avoid expensive operations.
8. **Domain Layer**:
   - Contain business logic and use cases (interactors) that orchestrate data flow and logic.
   - Use cases depend on repositories and are consumed by Blocs or View Models.
9. **Data Layer**:
   - Use **Repositories** as the Single Source of Truth (SSOT) for model data, handling caching, error management, and data refresh.
   - Only repositories mutate data; other components read from them.
   - Transform raw data from services into domain models for consumption by the Domain Layer or Blocs.
   - Use **Services** to wrap API endpoints, exposing asynchronous responses without maintaining state.
10. **Dependency Injection**:
    - Use **get_it** and **injectable** for dependency injection to provide components with dependencies, enhancing testability and flexibility.
    - Configure injectable to generate dependency injection code using **build_runner** for seamless integration.
11. **State Management**:
    - Use **flutter_bloc** for robust, predictable state management, ensuring separation of business logic from UI.
    - Implement Blocs to manage UI state and handle events triggered by user interactions.
    - Expose state changes to Views via `BlocBuilder` or `BlocSelector` to minimize rebuilds.
    - Keep state as local as possible to reduce complexity and optimize performance.
12. **Navigation**:
    - Use **auto_route** for declarative, type-safe navigation and route generation.
    - Leverage **flow_builder** for dynamic, state-driven navigation flows, especially for complex navigation logic.
    - Define routes in a centralized `app_router.dart` file with auto_route annotations, generating routes with **build_runner**.
13. **Backend Communication**:
    - Use **retrofit** with **dio** for type-safe HTTP requests and API client generation.
    - Implement **dio interceptors** for handling cross-cutting concerns like authentication, logging, and error handling.
    - Use **freezed** to generate immutable data classes and union types for robust data modeling.
    - Use **json_serializable** to automate JSON serialization/deserialization, integrated with **build_runner** for code generation.
14. **Code Generation**:
    - Rely on **mason** for generating boilerplate code, such as feature scaffolds, Blocs, and use cases, to enforce consistency.
    - Use **build_runner** to generate code for **injectable**, **auto_route**, **freezed**, and **json_serializable**.
15. **Localization**:
    - Use **i69n** for localization to manage translations efficiently.
    - Organize translation files per feature or module, generating localized strings with i69n's tooling.
16. **Performance Optimization**:
    - Use `const` constructors for widgets and immutable data classes to improve performance.
    - Implement pagination for large lists to optimize rendering and memory usage.
    - Use optimistic updates to enhance perceived responsiveness by updating the UI before operations complete.

## Data Flow and State

1. **Unidirectional Data Flow**: State flows from the Data Layer through the Domain Layer to the UI Layer. User interaction events flow in the opposite direction via Blocs.
2. **Single Source of Truth**: Data changes occur only in the SSOT (repositories in the Data Layer), not in the UI or Domain layers.
3. **Immutable State**: UI reflects immutable state from Blocs, triggering rebuilds only on state changes using `BlocBuilder` or `BlocSelector`.
4. **Minimal View Logic**: Views should be driven by state from Blocs, containing minimal logic for presentation only.

## Use Cases / Interactors

1. **Use Case Introduction**: Add use cases in the Domain Layer for complex, reused logic or when merging data from multiple repositories.
2. **Use Case Dependencies**: Use cases depend on repositories and are consumed by Blocs or View Models.
3. **Refactoring for Use Cases**: Introduce use cases when logic is repeatedly shared across Blocs, ensuring modularity.
4. **Code Generation for Use Cases**: Use **mason** to generate use case templates to maintain consistency.

## Extensibility and Testability

1. **Well-Defined Interfaces**: Ensure all components (Blocs, Repositories, Services) have clear inputs and outputs.
2. **Dependency Injection**: Use **get_it** and **injectable** to allow swapping implementations for testing or alternative configurations.
3. **Testing**:
   - Test Blocs by mocking repositories using **get_it** for dependency injection.
   - Test UI logic independently of widgets, using widget tests for Views.
   - Test use cases and services by mocking their dependencies.
4. **Replaceable Components**: Design components to be modular and independently testable.

## Best Practices

1. **Separation of Concerns**: Strictly adhere to clean architecture and layered separation.
2. **Dependency Injection**: Use **get_it** and **injectable** for all components to ensure testability and flexibility.
3. **State Management**: Use **flutter_bloc** as the default state management solution, adapting for app complexity.
4. **Storage**:
   - Use key-value storage (e.g., `shared_preferences`) for simple data like preferences.
   - Use SQL storage (e.g., `sqflite`) for complex relationships.
5. **Offline-First**:
   - Combine local and remote data sources in repositories to support offline-first strategies.
   - Implement synchronization logic to handle data consistency between local and remote sources.
6. **Widget Best Practices**:
   - Extract reusable widgets into separate components.
   - Prefer `StatelessWidget` over `StatefulWidget` unless dynamic state is required.
   - Keep `build` methods focused on rendering, avoiding expensive operations.
7. **Code Organization**:
   - Keep files focused on a single responsibility (e.g., one Bloc, one use case, one widget per file).
   - Limit file length for readability (e.g., <300 lines where feasible).
   - Group related functionality by feature or module.
   - Use `final` for fields and top-level variables to enforce immutability.
   - Make declarations private where possible and use `const` constructors for immutable classes.
8. **Navigation Best Practices**:
   - Use **auto_route** for type-safe routing and **flow_builder** for dynamic navigation flows.
   - Generate routes with **build_runner** to reduce boilerplate and errors.
9. **Backend Communication**:
   - Use **retrofit** and **dio** for API interactions, with **dio interceptors** for logging, authentication, and error handling.
   - Generate immutable data models with **freezed** and serialization logic with **json_serializable**.
10. **Code Generation**:
    - Use **mason** to create consistent boilerplate for features, Blocs, and use cases.
    - Automate code generation with **build_runner** for **injectable**, **auto_route**, **freezed**, and **json_serializable**.
11. **Localization**:
    - Use **i69n** to manage translations, generating localized strings per feature or module.
12. **Dart Best Practices**:
    - Follow Dart naming conventions and format code using `dart format`.
    - Use curly braces for all flow control statements to ensure clarity and prevent bugs.
    - Leverage `const` and `final` to enforce immutability and improve performance.
